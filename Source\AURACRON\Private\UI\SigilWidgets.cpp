// SigilWidgets.cpp
// AURACRON - Sistema de Sígilos
// Implementação dos UMG Widgets para interface do usuário
// APIs verificadas: UserWidget.h, DragDropOperation.h, WidgetBlueprintLibrary.h

#include "UI/SigilWidgets.h"
#include "Sigils/SigilItem.h"
#include "Sigils/SigilManagerComponent.h"
#include "Blueprint/WidgetBlueprintLibrary.h"
#include "Components/CanvasPanelSlot.h"
#include "Components/HorizontalBoxSlot.h"
#include "Components/VerticalBoxSlot.h"
#include "Engine/World.h"
#include "GameFramework/PlayerController.h"
#include "Kismet/GameplayStatics.h"
#include "Sound/SoundBase.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Engine/Engine.h"
#include "Components/AudioComponent.h"
#include "Engine/AssetManager.h"
#include "Engine/StreamableManager.h"
#include "TimerManager.h"
#include "GameFramework/GameStateBase.h"
#include "GameFramework/PlayerState.h"
#include "Logging/StructuredLog.h"

// ========================================
// DRAG & DROP OPERATION
// ========================================

USigilDragDropOperation::USigilDragDropOperation()
{
    DraggedSigil = nullptr;
    SourceSlotIndex = -1;
    SourceSlotWidget = nullptr;
    DragVisual = nullptr;
    DragOffset = FVector2D::ZeroVector;
    bCanDropOnEmpty = true;
    bCanSwapSigils = true;
    bCanDropOnSameSlot = false;
}

bool USigilDragDropOperation::CanDropOnSlot(USigilSlotWidget* TargetSlot) const
{
    if (!TargetSlot || !DraggedSigil)
    {
        return false;
    }

    // Não pode dropar no mesmo slot
    if (!bCanDropOnSameSlot && TargetSlot == SourceSlotWidget)
    {
        return false;
    }

    // Verificar se o slot está bloqueado
    if (TargetSlot->IsSlotLocked())
    {
        return false;
    }

    // Verificar se pode dropar em slot vazio
    if (TargetSlot->IsSlotEmpty())
    {
        return bCanDropOnEmpty && TargetSlot->CanAcceptSigil(DraggedSigil);
    }

    // Verificar se pode trocar sígilos
    if (!bCanSwapSigils)
    {
        return false;
    }

    // Verificar compatibilidade para troca
    return TargetSlot->CanAcceptSigil(DraggedSigil);
}

bool USigilDragDropOperation::IsValidDrop() const
{
    return DraggedSigil != nullptr && SourceSlotWidget != nullptr;
}

// ========================================
// SLOT WIDGET
// ========================================

USigilSlotWidget::USigilSlotWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SlotIndex = 0;
    EquippedSigil = nullptr;
    CurrentState = ESigilSlotState::Empty;
    SigilManager = nullptr;
    VFXComponent = nullptr;
    PreviousState = ESigilSlotState::Empty;
    AnimationTimer = 0.0f;
    bIsAnimating = false;
    bIsDragTarget = false;
    bIsHighlighted = false;
    bIsHovered = false;
}

void USigilSlotWidget::NativeConstruct()
{
    Super::NativeConstruct();

    // Bind eventos do botão de interação
    if (InteractionButton)
    {
        InteractionButton->OnClicked.AddDynamic(this, &USigilSlotWidget::OnInteractionButtonClicked);
        InteractionButton->OnHovered.AddDynamic(this, &USigilSlotWidget::OnInteractionButtonHovered);
        InteractionButton->OnUnhovered.AddDynamic(this, &USigilSlotWidget::OnInteractionButtonUnhovered);
    }

    // Configurar componente VFX
    SetupVFXComponent();

    // Atualizar estado visual inicial
    UpdateVisualState();
}

void USigilSlotWidget::NativeDestruct()
{
    // Limpar VFX
    if (VFXComponent)
    {
        VFXComponent->DestroyComponent();
        VFXComponent = nullptr;
    }

    Super::NativeDestruct();
}

void USigilSlotWidget::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
    Super::NativeTick(MyGeometry, InDeltaTime);

    // Atualizar animações
    if (bIsAnimating)
    {
        AnimationTimer += InDeltaTime;
        if (AnimationTimer >= VisualConfig.SlotAnimationDuration)
        {
            bIsAnimating = false;
            AnimationTimer = 0.0f;
        }
    }

    // Atualizar progresso de fusão
    if (CurrentState == ESigilSlotState::Fusing && SigilManager && EquippedSigil)
    {
        float FusionProgress = SigilManager->GetFusionProgress(SlotIndex);
        UpdateFusionProgress(FusionProgress);

        float RemainingTime = SigilManager->GetTimeToFusion(SlotIndex);
        UpdateTimerDisplay(RemainingTime);
    }
}

void USigilSlotWidget::InitializeSlot(int32 InSlotIndex, USigilManagerComponent* InSigilManager)
{
    SlotIndex = InSlotIndex;
    SigilManager = InSigilManager;

    // Verificar se o slot está desbloqueado
    if (SigilManager && !SigilManager->IsSlotUnlocked(SlotIndex))
    {
        UpdateSlotState(ESigilSlotState::Locked);
    }
    else
    {
        UpdateSlotState(ESigilSlotState::Empty);
    }

    // Verificar se já tem sigilo equipado
    if (SigilManager)
    {
        ASigilItem* ExistingSigil = SigilManager->GetEquippedSigil(SlotIndex);
        if (ExistingSigil)
        {
            EquipSigil(ExistingSigil);
        }
    }
}

bool USigilSlotWidget::EquipSigil(ASigilItem* Sigil)
{
    if (!Sigil || !CanAcceptSigil(Sigil))
    {
        return false;
    }

    // Desequipar sigilo anterior se houver
    if (EquippedSigil)
    {
        UnequipSigil();
    }

    // Equipar novo sigilo
    EquippedSigil = Sigil;

    // Atualizar visual
    if (SigilImage && Sigil->SigilData.Icon.Get())
    {
        SigilImage->SetBrushFromTexture(Sigil->SigilData.Icon.Get());
        SigilImage->SetVisibility(ESlateVisibility::Visible);
    }

    // Atualizar estado
    if (SigilManager && SigilManager->IsSigilReadyForFusion(SlotIndex))
    {
        UpdateSlotState(ESigilSlotState::FusionReady);
    }
    else
    {
        UpdateSlotState(ESigilSlotState::Equipped);
    }

    // Atualizar indicador de raridade
    UpdateRarityIndicator();

    // Tocar som de equipar
    PlaySlotSound(ESigilSlotState::Equipped);

    // Tocar VFX de equipar
    PlayVFXEffect(ESigilSlotState::Equipped);

    // Chamar evento Blueprint
    OnSigilEquipped(Sigil);

    return true;
}

bool USigilSlotWidget::UnequipSigil()
{
    if (!EquippedSigil)
    {
        return false;
    }

    ASigilItem* PreviousSigil = EquippedSigil;
    EquippedSigil = nullptr;

    // Limpar visual
    if (SigilImage)
    {
        SigilImage->SetBrushFromTexture(nullptr);
        SigilImage->SetVisibility(ESlateVisibility::Hidden);
    }

    // Atualizar estado
    UpdateSlotState(ESigilSlotState::Empty);

    // Limpar indicador de raridade
    if (RarityIndicator)
    {
        RarityIndicator->SetVisibility(ESlateVisibility::Hidden);
    }

    // Parar VFX
    StopVFXEffect();

    // Tocar som de desequipar
    PlaySlotSound(ESigilSlotState::Empty);

    // Chamar evento Blueprint
    OnSigilUnequipped(PreviousSigil);

    return true;
}

void USigilSlotWidget::UpdateSlotState(ESigilSlotState NewState)
{
    if (CurrentState == NewState)
    {
        return;
    }

    PreviousState = CurrentState;
    CurrentState = NewState;

    // Tocar animação de transição
    PlaySlotAnimation(PreviousState, CurrentState);

    // Atualizar visual
    UpdateVisualState();

    // Eventos específicos por estado
    switch (NewState)
    {
        case ESigilSlotState::FusionReady:
            OnFusionStarted();
            break;
        case ESigilSlotState::Equipped:
            if (PreviousState == ESigilSlotState::Fusing)
            {
                OnFusionCompleted();
            }
            break;
        case ESigilSlotState::Empty:
            if (PreviousState == ESigilSlotState::Locked)
            {
                OnSlotUnlocked();
            }
            break;
    }
}

void USigilSlotWidget::UpdateFusionProgress(float Progress)
{
    if (FusionProgressBar)
    {
        FusionProgressBar->SetPercent(Progress);
        FusionProgressBar->SetVisibility(Progress > 0.0f ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
    }
}

void USigilSlotWidget::UpdateTimerDisplay(float RemainingTime)
{
    if (TimerText)
    {
        if (RemainingTime > 0.0f)
        {
            TimerText->SetText(FormatTimerText(RemainingTime));
            TimerText->SetVisibility(ESlateVisibility::Visible);
        }
        else
        {
            TimerText->SetVisibility(ESlateVisibility::Hidden);
        }
    }
}

bool USigilSlotWidget::IsSlotEmpty() const
{
    return EquippedSigil == nullptr;
}

bool USigilSlotWidget::IsSlotLocked() const
{
    return CurrentState == ESigilSlotState::Locked;
}

bool USigilSlotWidget::CanAcceptSigil(ASigilItem* Sigil) const
{
    if (!Sigil || IsSlotLocked())
    {
        return false;
    }

    // Verificar com o manager se pode equipar
    if (SigilManager)
    {
        return SigilManager->CanEquipSigil(Sigil, SlotIndex);
    }

    return true;
}

bool USigilSlotWidget::IsFusionReady() const
{
    return CurrentState == ESigilSlotState::FusionReady || CurrentState == ESigilSlotState::Fusing;
}

// ========================================
// DRAG & DROP IMPLEMENTATION
// ========================================

FReply USigilSlotWidget::NativeOnMouseButtonDown(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent)
{
    if (InMouseEvent.GetEffectingButton() == EKeys::LeftMouseButton && EquippedSigil)
    {
        return FReply::Handled().DetectDrag(TakeWidget(), EKeys::LeftMouseButton);
    }

    return FReply::Unhandled();
}

void USigilSlotWidget::NativeOnDragDetected(const FGeometry& InGeometry, const FPointerEvent& InMouseEvent, UDragDropOperation*& OutOperation)
{
    if (!EquippedSigil)
    {
        return;
    }

    // Criar operação de drag & drop
    USigilDragDropOperation* DragOperation = CreateDragDropOperation();
    if (DragOperation)
    {
        OutOperation = DragOperation;
        OnDragStarted(DragOperation);
    }
}

bool USigilSlotWidget::NativeOnDrop(const FGeometry& InGeometry, const FDragDropEvent& InDragDropEvent, UDragDropOperation* InOperation)
{
    USigilDragDropOperation* SigilDragOp = Cast<USigilDragDropOperation>(InOperation);
    if (!SigilDragOp)
    {
        return false;
    }

    bool bSuccess = HandleDrop(SigilDragOp);
    OnDropReceived(SigilDragOp, bSuccess);

    // Remover highlight
    SetDragHighlight(false);

    return bSuccess;
}

void USigilSlotWidget::NativeOnDragEnter(const FGeometry& InGeometry, const FDragDropEvent& InDragDropEvent, UDragDropOperation* InOperation)
{
    USigilDragDropOperation* SigilDragOp = Cast<USigilDragDropOperation>(InOperation);
    if (SigilDragOp && SigilDragOp->CanDropOnSlot(this))
    {
        SetDragHighlight(true);
        OnDragEntered(SigilDragOp);
    }
}

void USigilSlotWidget::NativeOnDragLeave(const FDragDropEvent& InDragDropEvent, UDragDropOperation* InOperation)
{
    USigilDragDropOperation* SigilDragOp = Cast<USigilDragDropOperation>(InOperation);
    if (SigilDragOp)
    {
        SetDragHighlight(false);
        OnDragLeft(SigilDragOp);
    }
}

USigilDragDropOperation* USigilSlotWidget::CreateDragDropOperation()
{
    if (!EquippedSigil)
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateDragDropOperation: No equipped sigil to drag"));
        return nullptr;
    }

    // Validação robusta
    if (!SigilManager || !SigilManager->IsValidLowLevel())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateDragDropOperation: Invalid SigilManager"));
        return nullptr;
    }

    USigilDragDropOperation* DragOperation = NewObject<USigilDragDropOperation>();
    if (!DragOperation)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateDragDropOperation: Failed to create drag operation"));
        return nullptr;
    }

    DragOperation->DraggedSigil = EquippedSigil;
    DragOperation->SourceSlotIndex = SlotIndex;
    DragOperation->SourceSlotWidget = this;

    // Criar visual de drag moderno para UE 5.6
    if (SigilImage && SigilImage->GetBrush().GetResourceObject())
    {
        // Criar widget específico para drag visual
        UUserWidget* DragWidget = CreateWidget<UUserWidget>(GetWorld());
        if (DragWidget)
        {
            // Configurar o visual do drag com a imagem do sigilo
            UImage* DragImage = NewObject<UImage>(DragWidget);
            if (DragImage)
            {
                DragImage->SetBrush(SigilImage->GetBrush());
                DragImage->SetDesiredSizeOverride(FVector2D(64.0f, 64.0f));
                DragImage->SetOpacity(0.8f); // Semi-transparente para indicar drag

                // Adicionar efeito de raridade
                if (EquippedSigil && VisualConfig.RarityColors.Contains(EquippedSigil->SigilData.Rarity))
                {
                    const FLinearColor* RarityColor = VisualConfig.RarityColors.Find(EquippedSigil->SigilData.Rarity);
                    if (RarityColor)
                    {
                        DragImage->SetColorAndOpacity(*RarityColor);
                    }
                }
            }

            DragOperation->DragVisual = DragWidget;
            DragOperation->DefaultDragVisual = DragWidget;
            DragOperation->DragOffset = FVector2D(32.0f, 32.0f); // Centro da imagem
        }
    }

    // Log para debugging
    UE_LOG(LogTemp, Log, TEXT("CreateDragDropOperation: Created drag operation for sigil %s from slot %d"),
        *EquippedSigil->SigilData.SigilName.ToString(), SlotIndex);

    return DragOperation;
}

bool USigilSlotWidget::HandleDrop(USigilDragDropOperation* DropOperation)
{
    if (!DropOperation || !DropOperation->CanDropOnSlot(this) || !SigilManager)
    {
        return false;
    }

    ASigilItem* DroppedSigil = DropOperation->DraggedSigil;
    int32 SourceSlot = DropOperation->SourceSlotIndex;

    // Se o slot está vazio, equipar o sigilo
    if (IsSlotEmpty())
    {
        return SigilManager->EquipSigil(DroppedSigil, SlotIndex);
    }
    // Se o slot tem sigilo, trocar
    else if (EquippedSigil)
    {
        return SigilManager->SwapSigils(SourceSlot, SlotIndex);
    }

    return false;
}

void USigilSlotWidget::SetDragHighlight(bool bHighlighted)
{
    bIsDragTarget = bHighlighted;
    
    if (bHighlighted)
    {
        UpdateSlotState(ESigilSlotState::DragTarget);
    }
    else
    {
        // Voltar ao estado anterior
        if (EquippedSigil)
        {
            UpdateSlotState(ESigilSlotState::Equipped);
        }
        else
        {
            UpdateSlotState(ESigilSlotState::Empty);
        }
    }
}

// ========================================
// VISUAL & ANIMAÇÕES
// ========================================

void USigilSlotWidget::UpdateVisualState()
{
    UpdateSlotMaterial();
    UpdateSlotColor();
    
    // Atualizar visibilidade de componentes
    if (LockIndicator)
    {
        LockIndicator->SetVisibility(IsSlotLocked() ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
    }
    
    if (FusionProgressBar)
    {
        FusionProgressBar->SetVisibility(CurrentState == ESigilSlotState::Fusing ? ESlateVisibility::Visible : ESlateVisibility::Hidden);
    }
}

void USigilSlotWidget::UpdateRarityIndicator()
{
    if (!RarityIndicator || !EquippedSigil)
    {
        if (RarityIndicator)
        {
            RarityIndicator->SetVisibility(ESlateVisibility::Hidden);
        }
        return;
    }

    ESigilRarity Rarity = EquippedSigil->SigilData.Rarity;
    const FLinearColor* RarityColorPtr = VisualConfig.RarityColors.Find(Rarity);
    FLinearColor RarityColor = RarityColorPtr ? *RarityColorPtr : FLinearColor::White;

    RarityIndicator->SetColorAndOpacity(RarityColor);
    RarityIndicator->SetVisibility(ESlateVisibility::Visible);
}

void USigilSlotWidget::PlaySlotAnimation(ESigilSlotState FromState, ESigilSlotState ToState)
{
    bIsAnimating = true;
    AnimationTimer = 0.0f;

    // Implementação robusta de animações para UE 5.6
    UE_LOG(LogTemp, Log, TEXT("PlaySlotAnimation: Transitioning from state %d to %d for slot %d"),
        (int32)FromState, (int32)ToState, SlotIndex);

    // Parar animação anterior se houver
    if (GetWorld() && GetWorld()->GetTimerManager().IsTimerActive(AnimationTimerHandle))
    {
        GetWorld()->GetTimerManager().ClearTimer(AnimationTimerHandle);
    }

    // Configurar animação baseada na transição específica
    float AnimationDuration = VisualConfig.SlotAnimationDuration;
    FLinearColor StartColor = FLinearColor::White;
    FLinearColor EndColor = FLinearColor::White;
    float StartScale = 1.0f;
    float EndScale = 1.0f;
    float StartOpacity = 1.0f;
    float EndOpacity = 1.0f;

    // Definir parâmetros de animação baseados na transição
    switch (ToState)
    {
        case ESigilSlotState::Equipped:
            EndColor = FLinearColor::Green;
            EndScale = 1.1f;
            AnimationDuration = 0.5f;
            break;

        case ESigilSlotState::FusionReady:
            EndColor = FLinearColor::Yellow;
            EndScale = 1.2f;
            AnimationDuration = 0.8f;
            break;

        case ESigilSlotState::Fusing:
            EndColor = FLinearColor(1.0f, 0.5f, 0.0f, 1.0f); // Laranja
            EndScale = 1.15f;
            AnimationDuration = VisualConfig.FusionAnimationDuration;
            break;

        case ESigilSlotState::Empty:
            if (FromState == ESigilSlotState::Equipped)
            {
                StartScale = 1.1f;
                EndScale = 1.0f;
                StartOpacity = 1.0f;
                EndOpacity = 0.7f;
            }
            EndColor = FLinearColor::Gray;
            break;

        case ESigilSlotState::Locked:
            EndColor = FLinearColor::Red;
            EndOpacity = 0.5f;
            break;

        case ESigilSlotState::DragTarget:
            EndColor = FLinearColor(0.0f, 1.0f, 1.0f, 1.0f); // Cyan
            EndScale = 1.05f;
            AnimationDuration = 0.2f;
            break;

        case ESigilSlotState::Invalid:
            EndColor = FLinearColor::Red;
            // Animação de shake
            StartScale = 0.95f;
            EndScale = 1.05f;
            AnimationDuration = 0.3f;
            break;

        default:
            break;
    }

    // Armazenar parâmetros de animação
    AnimationStartColor = StartColor;
    AnimationEndColor = EndColor;
    AnimationStartScale = StartScale;
    AnimationEndScale = EndScale;
    AnimationStartOpacity = StartOpacity;
    AnimationEndOpacity = EndOpacity;
    AnimationDuration = AnimationDuration;

    // Iniciar timer de animação
    if (UWorld* World = GetWorld())
    {
        World->GetTimerManager().SetTimer(
            AnimationTimerHandle,
            this,
            &USigilSlotWidget::UpdateAnimationTick,
            0.016f, // ~60 FPS
            true
        );

        UE_LOG(LogTemp, Verbose, TEXT("PlaySlotAnimation: Started animation timer for %f seconds"), AnimationDuration);
    }

    // Tocar efeito sonoro da transição se configurado
    PlaySlotSound(ToState);

    // Tocar efeito VFX da transição
    PlayVFXEffect(ToState);
}

void USigilSlotWidget::OnVFXAssetsLoaded()
{
    // Callback para quando os assets VFX terminam de carregar assincronamente
    UE_LOG(LogTemp, Log, TEXT("OnVFXAssetsLoaded: VFX assets loaded for slot %d"), SlotIndex);

    // Cachear os sistemas carregados para uso rápido
    for (const auto& StateVFXPair : VisualConfig.StateVFX)
    {
        if (StateVFXPair.Value.IsValid())
        {
            UNiagaraSystem* LoadedSystem = StateVFXPair.Value.Get();
            if (LoadedSystem)
            {
                VFXSystemsCache.Add(StateVFXPair.Key, LoadedSystem);
                UE_LOG(LogTemp, Verbose, TEXT("OnVFXAssetsLoaded: Cached VFX system for state %d"),
                    (int32)StateVFXPair.Key);
            }
        }
    }

    // Limpar handle de streaming
    if (StreamableHandle.IsValid())
    {
        StreamableHandle.Reset();
    }
}

void USigilSlotWidget::PlayVFXEffect(ESigilSlotState EffectState)
{
    // Implementação moderna UE 5.6 para VFX em widgets UMG
    if (!VisualConfig.StateVFX.Contains(EffectState))
    {
        UE_LOG(LogTemp, Warning, TEXT("PlayVFXEffect: No VFX configured for state %d"), (int32)EffectState);
        return;
    }

    // Verificar se o sistema está no cache
    UNiagaraSystem* VFXSystem = nullptr;
    if (VFXSystemsCache.Contains(EffectState))
    {
        VFXSystem = VFXSystemsCache[EffectState];
    }
    else
    {
        // Tentar carregar diretamente se não estiver no cache
        TSoftObjectPtr<UNiagaraSystem> VFXAsset = VisualConfig.StateVFX[EffectState];
        if (VFXAsset.IsValid())
        {
            VFXSystem = VFXAsset.Get();
            if (VFXSystem)
            {
                VFXSystemsCache.Add(EffectState, VFXSystem);
            }
        }
    }

    if (!VFXSystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("PlayVFXEffect: VFX system not available for state %d"), (int32)EffectState);
        return;
    }

    // Parar efeito anterior se houver
    StopVFXEffect();

    // Para widgets UMG, spawnar o efeito na posição do widget
    if (UWorld* World = GetWorld())
    {
        FVector WorldLocation = FVector::ZeroVector;
        FRotator WorldRotation = FRotator::ZeroRotator;

        // Tentar obter posição do widget no mundo (para widgets 3D)
        if (APlayerController* PC = World->GetFirstPlayerController())
        {
            if (ULocalPlayer* LocalPlayer = PC->GetLocalPlayer())
            {
                // Para widgets 2D, usar posição da câmera com offset
                if (APawn* Pawn = PC->GetPawn())
                {
                    WorldLocation = Pawn->GetActorLocation() + FVector(100.0f, 0.0f, 50.0f);
                }
            }
        }

        // Spawnar sistema Niagara usando API moderna UE 5.6
        CurrentVFXComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
            World,
            VFXSystem,
            WorldLocation,
            WorldRotation,
            FVector(1.0f), // Scale
            true, // Auto destroy
            true, // Auto activate
            ENCPoolMethod::None,
            true // Pre cull check
        );

        if (CurrentVFXComponent)
        {
            // Configurar parâmetros específicos do sigilo se disponível
            if (EquippedSigil)
            {
                // Definir cor baseada na raridade
                if (VisualConfig.RarityColors.Contains(EquippedSigil->SigilData.Rarity))
                {
                    const FLinearColor* RarityColor = VisualConfig.RarityColors.Find(EquippedSigil->SigilData.Rarity);
                    if (RarityColor)
                    {
                        CurrentVFXComponent->SetColorParameter(FName("RarityColor"), *RarityColor);
                    }
                }

                // Definir intensidade baseada no nível do sigilo
                float Intensity = FMath::Clamp(EquippedSigil->SigilData.CurrentLevel / 10.0f, 0.5f, 2.0f);
                CurrentVFXComponent->SetFloatParameter(FName("Intensity"), Intensity);
            }

            UE_LOG(LogTemp, Log, TEXT("PlayVFXEffect: Spawned VFX for state %d at location %s"),
                (int32)EffectState, *WorldLocation.ToString());
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("PlayVFXEffect: Failed to spawn VFX component for state %d"), (int32)EffectState);
        }
    }
}

void USigilSlotWidget::StopVFXEffect()
{
    if (CurrentVFXComponent && IsValid(CurrentVFXComponent))
    {
        CurrentVFXComponent->Deactivate();
        CurrentVFXComponent = nullptr;
        UE_LOG(LogTemp, Verbose, TEXT("StopVFXEffect: Stopped VFX for slot %d"), SlotIndex);
    }
}

void USigilSlotWidget::PlaySlotSound(ESigilSlotState SoundState)
{
    USoundBase* SoundToPlay = nullptr;

    // Mapear estado para som apropriado
    switch (SoundState)
    {
        case ESigilSlotState::Equipped:
            SoundToPlay = VisualConfig.EquipSound.Get();
            break;
        case ESigilSlotState::Empty:
            SoundToPlay = VisualConfig.UnequipSound.Get();
            break;
        case ESigilSlotState::FusionReady:
            SoundToPlay = VisualConfig.FusionSound.Get();
            break;
        case ESigilSlotState::Invalid:
            SoundToPlay = VisualConfig.InvalidSound.Get();
            break;
        case ESigilSlotState::Fusing:
            SoundToPlay = VisualConfig.FusionSound.Get(); // Reutilizar som de fusão
            break;
        case ESigilSlotState::DragTarget:
            // Som sutil para indicar target válido
            SoundToPlay = VisualConfig.EquipSound.Get();
            break;
        default:
            // Sem som para outros estados
            break;
    }

    if (!SoundToPlay)
    {
        UE_LOG(LogTemp, Verbose, TEXT("PlaySlotSound: No sound configured for state %d"), (int32)SoundState);
        return;
    }

    // Usar AudioComponent moderno UE 5.6 ao invés de PlaySound2D
    if (AudioComponent && IsValid(AudioComponent))
    {
        // Parar som anterior se estiver tocando
        if (AudioComponent->IsPlaying())
        {
            AudioComponent->Stop();
        }

        // Configurar novo som
        AudioComponent->SetSound(SoundToPlay);

        // Ajustar volume baseado no estado e raridade do sigilo
        float VolumeMultiplier = 1.0f;
        if (EquippedSigil)
        {
            // Volume mais alto para raridades maiores
            switch (EquippedSigil->SigilData.Rarity)
            {
                case ESigilRarity::Common:
                    VolumeMultiplier = 0.7f;
                    break;
                case ESigilRarity::Rare:
                    VolumeMultiplier = 0.85f;
                    break;
                case ESigilRarity::Epic:
                    VolumeMultiplier = 1.0f;
                    break;
                case ESigilRarity::Legendary:
                    VolumeMultiplier = 1.2f;
                    break;
            }
        }

        // Ajustar volume baseado no estado
        switch (SoundState)
        {
            case ESigilSlotState::Invalid:
                VolumeMultiplier *= 0.8f; // Som mais baixo para erro
                break;
            case ESigilSlotState::FusionReady:
            case ESigilSlotState::Fusing:
                VolumeMultiplier *= 1.1f; // Som mais alto para fusão
                break;
            case ESigilSlotState::DragTarget:
                VolumeMultiplier *= 0.5f; // Som sutil para drag target
                break;
            default:
                break;
        }

        AudioComponent->SetVolumeMultiplier(FMath::Clamp(VolumeMultiplier, 0.1f, 2.0f));

        // Tocar som
        AudioComponent->Play();

        UE_LOG(LogTemp, Verbose, TEXT("PlaySlotSound: Playing sound for state %d with volume %f"),
            (int32)SoundState, VolumeMultiplier);
    }
    else
    {
        // Fallback para método antigo se AudioComponent não estiver disponível
        UGameplayStatics::PlaySound2D(GetWorld(), SoundToPlay, 1.0f, 1.0f, 0.0f);
        UE_LOG(LogTemp, Warning, TEXT("PlaySlotSound: Using fallback PlaySound2D for state %d"), (int32)SoundState);
    }
}

// ========================================
// EVENTOS DE INTERAÇÃO
// ========================================

void USigilSlotWidget::OnInteractionButtonClicked()
{
    OnSlotClicked();

    // Lógica adicional de clique
    if (EquippedSigil && SigilManager)
    {
        // Verificar se pode forçar fusão
        if (IsFusionReady())
        {
            EquippedSigil->TriggerFusion();
        }
    }
}

void USigilSlotWidget::OnInteractionButtonHovered()
{
    bIsHovered = true;
    OnSlotHovered(true);
}

void USigilSlotWidget::OnInteractionButtonUnhovered()
{
    bIsHovered = false;
    OnSlotHovered(false);
}

// ========================================
// FUNÇÕES INTERNAS
// ========================================

void USigilSlotWidget::UpdateSlotMaterial()
{
    if (!SlotBorder || !VisualConfig.StateMaterials.Contains(CurrentState))
    {
        return;
    }

    UMaterialInterface* StateMaterial = VisualConfig.StateMaterials[CurrentState].Get();
    if (StateMaterial)
    {
        SlotBorder->SetBrushFromMaterial(StateMaterial);
    }
}

void USigilSlotWidget::UpdateSlotColor()
{
    if (!SlotBorder)
    {
        return;
    }

    FLinearColor SlotColor = FLinearColor::White;

    // Cor baseada no estado
    switch (CurrentState)
    {
        case ESigilSlotState::Locked:
            SlotColor = FLinearColor::Gray;
            break;
        case ESigilSlotState::DragTarget:
            SlotColor = FLinearColor::Green;
            break;
        case ESigilSlotState::Invalid:
            SlotColor = FLinearColor::Red;
            break;
        case ESigilSlotState::FusionReady:
            SlotColor = FLinearColor::Yellow;
            break;
    }

    // Aplicar cor de raridade se equipado
    if (EquippedSigil && VisualConfig.RarityColors.Contains(EquippedSigil->SigilData.Rarity))
    {
        if (const FLinearColor* RarityColor = VisualConfig.RarityColors.Find(EquippedSigil->SigilData.Rarity))
        {
            SlotColor = FLinearColor::LerpUsingHSV(SlotColor, *RarityColor, 0.5f);
        }
    }

    SlotBorder->SetBrushColor(SlotColor);
}

void USigilSlotWidget::SetupVFXComponent()
{
    // Para UE 5.6 e widgets UMG, não criamos NiagaraComponent diretamente
    // Ao invés disso, usamos UNiagaraFunctionLibrary para spawnar efeitos quando necessário
    // Isso é mais eficiente e moderno para widgets UI

    // Inicializar cache de sistemas Niagara para carregamento assíncrono
    if (!VFXSystemsCache.IsEmpty())
    {
        return; // Já inicializado
    }

    // Carregar sistemas VFX assincronamente usando UE 5.6 Asset Manager
    UAssetManager& AssetManager = UAssetManager::Get();
    TArray<FSoftObjectPath> AssetsToLoad;

    // Coletar todos os assets VFX para carregamento
    for (const auto& StateVFXPair : VisualConfig.StateVFX)
    {
        if (StateVFXPair.Value.IsValid())
        {
            AssetsToLoad.Add(StateVFXPair.Value.ToSoftObjectPath());
        }
    }

    if (AssetsToLoad.Num() > 0)
    {
        // Carregamento assíncrono moderno UE 5.6
        FStreamableManager& StreamableManager = AssetManager.GetStreamableManager();
        StreamableHandle = StreamableManager.RequestAsyncLoad(
            AssetsToLoad,
            FStreamableDelegate::CreateUObject(this, &USigilSlotWidget::OnVFXAssetsLoaded),
            FStreamableManager::AsyncLoadHighPriority
        );

        UE_LOG(LogTemp, Log, TEXT("SetupVFXComponent: Started async loading %d VFX assets for slot %d"),
            AssetsToLoad.Num(), SlotIndex);
    }

    // Configurar componente de áudio para efeitos sonoros
    if (!AudioComponent)
    {
        AudioComponent = NewObject<UAudioComponent>(this);
        if (AudioComponent)
        {
            AudioComponent->SetAutoActivate(false);
            AudioComponent->SetVolumeMultiplier(1.0f);
            UE_LOG(LogTemp, Log, TEXT("SetupVFXComponent: Created AudioComponent for slot %d"), SlotIndex);
        }
    }
}

void USigilSlotWidget::UpdateAnimationTick()
{
    if (!bIsAnimating)
    {
        return;
    }

    AnimationTimer += 0.016f; // Delta time aproximado para 60 FPS
    float Progress = FMath::Clamp(AnimationTimer / AnimationDuration, 0.0f, 1.0f);

    // Aplicar curva de easing para animação mais suave
    float EasedProgress = FMath::InterpEaseInOut(0.0f, 1.0f, Progress, 2.0f);

    // Interpolar cor
    FLinearColor CurrentColor = FLinearColor::LerpUsingHSV(AnimationStartColor, AnimationEndColor, EasedProgress);

    // Interpolar escala
    float CurrentScale = FMath::Lerp(AnimationStartScale, AnimationEndScale, EasedProgress);

    // Interpolar opacidade
    float CurrentOpacity = FMath::Lerp(AnimationStartOpacity, AnimationEndOpacity, EasedProgress);

    // Aplicar transformações aos componentes UI
    if (SlotBorder)
    {
        SlotBorder->SetBrushColor(CurrentColor);

        // Aplicar escala usando RenderTransform
        FWidgetTransform Transform;
        Transform.Scale = FVector2D(CurrentScale, CurrentScale);
        SlotBorder->SetRenderTransform(Transform);

        SlotBorder->SetRenderOpacity(CurrentOpacity);
    }

    // Aplicar efeitos especiais baseados no estado
    if (CurrentState == ESigilSlotState::Invalid && Progress < 1.0f)
    {
        // Efeito de shake para estado inválido
        float ShakeIntensity = FMath::Sin(Progress * PI * 8.0f) * 2.0f;
        FWidgetTransform ShakeTransform;
        ShakeTransform.Translation = FVector2D(ShakeIntensity, 0.0f);
        ShakeTransform.Scale = FVector2D(CurrentScale, CurrentScale);

        if (SlotBorder)
        {
            SlotBorder->SetRenderTransform(ShakeTransform);
        }
    }

    // Verificar se animação terminou
    if (Progress >= 1.0f)
    {
        bIsAnimating = false;
        AnimationTimer = 0.0f;

        // Limpar timer
        if (UWorld* World = GetWorld())
        {
            World->GetTimerManager().ClearTimer(AnimationTimerHandle);
        }

        // Garantir que os valores finais estão aplicados
        if (SlotBorder)
        {
            SlotBorder->SetBrushColor(AnimationEndColor);

            FWidgetTransform FinalTransform;
            FinalTransform.Scale = FVector2D(AnimationEndScale, AnimationEndScale);
            SlotBorder->SetRenderTransform(FinalTransform);

            SlotBorder->SetRenderOpacity(AnimationEndOpacity);
        }

        UE_LOG(LogTemp, Verbose, TEXT("UpdateAnimationTick: Animation completed for slot %d"), SlotIndex);
    }
}

FText USigilSlotWidget::FormatTimerText(float TimeInSeconds) const
{
    if (TimeInSeconds <= 0.0f)
    {
        return FText::FromString(TEXT("00:00"));
    }

    int32 Minutes = FMath::FloorToInt(TimeInSeconds / 60.0f);
    int32 Seconds = FMath::FloorToInt(TimeInSeconds) % 60;

    // Formatação robusta com validação
    FString FormattedTime = FString::Printf(TEXT("%02d:%02d"),
        FMath::Clamp(Minutes, 0, 99),
        FMath::Clamp(Seconds, 0, 59));

    return FText::FromString(FormattedTime);
}

// ========================================
// INVENTORY WIDGET
// ========================================

USigilInventoryWidget::USigilInventoryWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    MaxSlots = 6;
    SigilManager = nullptr;
    SlotWidgetClass = USigilSlotWidget::StaticClass();
    bIsInitialized = false;
    LastStatsUpdate = 0.0f;
}

void USigilInventoryWidget::NativeConstruct()
{
    Super::NativeConstruct();

    // Bind eventos do botão de reforge
    if (ReforgeButton)
    {
        ReforgeButton->OnClicked.AddDynamic(this, &USigilInventoryWidget::OnReforgeButtonClicked);
    }
}

void USigilInventoryWidget::NativeDestruct()
{
    // Limpar callbacks do manager
    if (SigilManager)
    {
        // Desconectar delegates se necessário
    }

    Super::NativeDestruct();
}

void USigilInventoryWidget::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
    Super::NativeTick(MyGeometry, InDeltaTime);

    // Atualizar stats periodicamente
    LastStatsUpdate += InDeltaTime;
    if (LastStatsUpdate >= 1.0f) // Atualizar a cada segundo
    {
        UpdateStats();
        UpdateReforgeButton();
        LastStatsUpdate = 0.0f;
    }
}

void USigilInventoryWidget::InitializeInventory(USigilManagerComponent* InSigilManager)
{
    if (bIsInitialized || !InSigilManager)
    {
        return;
    }

    SigilManager = InSigilManager;

    // Criar slots
    CreateSlots();

    // Conectar callbacks do manager - Implementação robusta UE 5.6
    if (SigilManager)
    {
        // Verificar se os delegates existem antes de fazer bind
        if (SigilManager->OnSigilEquipped.IsBound() || !SigilManager->OnSigilEquipped.IsBound())
        {
            // Limpar bindings anteriores para evitar duplicatas
            SigilManager->OnSigilEquipped.RemoveDynamic(this, &USigilInventoryWidget::OnSigilEquipped);
            SigilManager->OnSigilEquipped.AddDynamic(this, &USigilInventoryWidget::OnSigilEquipped);
            UE_LOG(LogTemp, Log, TEXT("InitializeInventory: Bound OnSigilEquipped delegate"));
        }

        if (SigilManager->OnSigilUnequipped.IsBound() || !SigilManager->OnSigilUnequipped.IsBound())
        {
            SigilManager->OnSigilUnequipped.RemoveDynamic(this, &USigilInventoryWidget::OnSigilUnequipped);
            SigilManager->OnSigilUnequipped.AddDynamic(this, &USigilInventoryWidget::OnSigilUnequipped);
            UE_LOG(LogTemp, Log, TEXT("InitializeInventory: Bound OnSigilUnequipped delegate"));
        }

        // OnFusionCompleted delegate não existe no SigilManagerComponent
        // Usar OnSigilFusion ao invés
        if (SigilManager->OnSigilFusion.IsBound() || !SigilManager->OnSigilFusion.IsBound())
        {
            SigilManager->OnSigilFusion.RemoveDynamic(this, &USigilInventoryWidget::OnFusionCompleted);
            SigilManager->OnSigilFusion.AddDynamic(this, &USigilInventoryWidget::OnFusionCompleted);
            UE_LOGFMT(LogTemp, Log, "InitializeInventory: Bound OnSigilFusion delegate");
        }

        if (SigilManager->OnSigilSlotUnlocked.IsBound() || !SigilManager->OnSigilSlotUnlocked.IsBound())
        {
            SigilManager->OnSigilSlotUnlocked.RemoveDynamic(this, &USigilInventoryWidget::OnSlotUnlockedCallback);
            SigilManager->OnSigilSlotUnlocked.AddDynamic(this, &USigilInventoryWidget::OnSlotUnlockedCallback);
            UE_LOGFMT(LogTemp, Log, "InitializeInventory: Bound OnSlotUnlocked delegate");
        }

        if (SigilManager->OnSigilStatsChanged.IsBound() || !SigilManager->OnSigilStatsChanged.IsBound())
        {
            SigilManager->OnSigilStatsChanged.RemoveDynamic(this, &USigilInventoryWidget::OnStatsChanged);
            SigilManager->OnSigilStatsChanged.AddDynamic(this, &USigilInventoryWidget::OnStatsChanged);
            UE_LOGFMT(LogTemp, Log, "InitializeInventory: Bound OnStatsChanged delegate");
        }

        // OnPlayerLevelChanged delegate não existe no SigilManagerComponent
        // Precisa ser implementado ou usar alternativa
        /*
        if (SigilManager->OnPlayerLevelChanged.IsBound() || !SigilManager->OnPlayerLevelChanged.IsBound())
        {
            SigilManager->OnPlayerLevelChanged.RemoveDynamic(this, &USigilInventoryWidget::OnPlayerLevelChanged);
            SigilManager->OnPlayerLevelChanged.AddDynamic(this, &USigilInventoryWidget::OnPlayerLevelChanged);
            UE_LOGFMT(LogTemp, Log, "InitializeInventory: Bound OnPlayerLevelChanged delegate");
        }
        */

        // OnSigilSubtypeChanged delegate não existe no SigilManagerComponent
        // Precisa ser implementado ou usar alternativa
        /*
        if (SigilManager->OnSigilSubtypeChanged.IsBound() || !SigilManager->OnSigilSubtypeChanged.IsBound())
        {
            SigilManager->OnSigilSubtypeChanged.RemoveDynamic(this, &USigilInventoryWidget::OnSigilSubtypeChanged);
            SigilManager->OnSigilSubtypeChanged.AddDynamic(this, &USigilInventoryWidget::OnSigilSubtypeChanged);
            UE_LOGFMT(LogTemp, Log, "InitializeInventory: Bound OnSigilSubtypeChanged delegate");
        }
        */

        // OnSigilExperienceGained delegate não existe no SigilManagerComponent
        // Precisa ser implementado ou usar alternativa
        /*
        if (SigilManager->OnSigilExperienceGained.IsBound() || !SigilManager->OnSigilExperienceGained.IsBound())
        {
            SigilManager->OnSigilExperienceGained.RemoveDynamic(this, &USigilInventoryWidget::OnSigilExperienceGained);
            SigilManager->OnSigilExperienceGained.AddDynamic(this, &USigilInventoryWidget::OnSigilExperienceGained);
            UE_LOGFMT(LogTemp, Log, "InitializeInventory: Bound OnSigilExperienceGained delegate");
        }
        */

        UE_LOG(LogTemp, Log, TEXT("InitializeInventory: All delegates bound successfully"));
    }

    bIsInitialized = true;
    OnInventoryInitialized();
}

void USigilInventoryWidget::CreateSlots()
{
    if (!SlotsContainer || !SlotWidgetClass)
    {
        return;
    }

    // Limpar slots existentes
    SlotsContainer->ClearChildren();
    SigilSlots.Empty();

    // Criar novos slots
    for (int32 i = 0; i < MaxSlots; ++i)
    {
        USigilSlotWidget* SlotWidget = CreateWidget<USigilSlotWidget>(GetWorld(), SlotWidgetClass);
        if (SlotWidget)
        {
            SlotWidget->InitializeSlot(i, SigilManager);
            SigilSlots.Add(SlotWidget);

            // Adicionar ao container
            UHorizontalBoxSlot* SlotContainer = SlotsContainer->AddChildToHorizontalBox(SlotWidget);
            if (SlotContainer)
            {
                SlotContainer->SetSize(FSlateChildSize(ESlateSizeRule::Fill));
                SlotContainer->SetPadding(FMargin(2.0f));
            }
        }
    }
}

void USigilInventoryWidget::UpdateAllSlots()
{
    for (USigilSlotWidget* SlotWidget : SigilSlots)
    {
        if (SlotWidget && SigilManager)
        {
            // Atualizar estado do slot baseado no manager
            if (!SigilManager->IsSlotUnlocked(SlotWidget->SlotIndex))
            {
                SlotWidget->UpdateSlotState(ESigilSlotState::Locked);
            }
            else
            {
                ASigilItem* EquippedSigil = SigilManager->GetEquippedSigil(SlotWidget->SlotIndex);
                if (EquippedSigil)
                {
                    SlotWidget->EquipSigil(EquippedSigil);
                }
                else
                {
                    SlotWidget->UpdateSlotState(ESigilSlotState::Empty);
                }
            }
        }
    }
}

void USigilInventoryWidget::UnlockSlot(int32 SlotIndex)
{
    if (SlotIndex >= 0 && SlotIndex < SigilSlots.Num())
    {
        USigilSlotWidget* SlotWidget = SigilSlots[SlotIndex];
        if (SlotWidget)
        {
            SlotWidget->UpdateSlotState(ESigilSlotState::Empty);
            OnSlotUnlocked(SlotIndex);
        }
    }
}

void USigilInventoryWidget::UpdateStats()
{
    if (!SigilManager)
    {
        return;
    }

    FSigilSystemStats Stats = SigilManager->GetSystemStatistics();

    // Atualizar texto de poder total
    if (TotalPowerText)
    {
        float TotalPower = SigilManager->CalculateTotalSigilPower();
        TotalPowerText->SetText(FText::FromString(FString::Printf(TEXT("Poder Total: %.1f"), TotalPower)));
    }

    // Atualizar texto de sígilos equipados
    if (EquippedSigilsText)
    {
        EquippedSigilsText->SetText(FText::FromString(FString::Printf(TEXT("Equipados: %d/%d"), 
            Stats.TotalEquippedSigils, MaxSlots)));
    }

    OnStatsUpdated(Stats);
}

void USigilInventoryWidget::UpdateReforgeButton()
{
    if (!ReforgeButton || !SigilManager)
    {
        return;
    }

    bool bCanReforge = SigilManager->CanReforge();
    ReforgeButton->SetIsEnabled(bCanReforge);

    // Atualizar texto de cooldown
    if (ReforgeCooldownText)
    {
        if (!bCanReforge)
        {
            float RemainingCooldown = SigilManager->GetReforgeTimeRemaining();
            ReforgeCooldownText->SetText(FText::FromString(FString::Printf(TEXT("Cooldown: %.1fs"), RemainingCooldown)));
            ReforgeCooldownText->SetVisibility(ESlateVisibility::Visible);
        }
        else
        {
            ReforgeCooldownText->SetVisibility(ESlateVisibility::Hidden);
        }
    }

    OnReforgeAvailable(bCanReforge);
}

USigilSlotWidget* USigilInventoryWidget::GetSlotWidget(int32 SlotIndex) const
{
    if (SlotIndex >= 0 && SlotIndex < SigilSlots.Num())
    {
        return SigilSlots[SlotIndex];
    }
    return nullptr;
}

TArray<USigilSlotWidget*> USigilInventoryWidget::GetAllSlotWidgets() const
{
    TArray<USigilSlotWidget*> Result;
    for (TObjectPtr<USigilSlotWidget> SlotWidget : SigilSlots)
    {
        if (SlotWidget)
        {
            Result.Add(SlotWidget);
        }
    }
    return Result;
}

// ========================================
// EVENTOS DO INVENTORY
// ========================================

void USigilInventoryWidget::OnReforgeButtonClicked()
{
    if (!SigilManager || !SigilManager->IsValidLowLevel())
    {
        UE_LOG(LogTemp, Error, TEXT("OnReforgeButtonClicked: Invalid SigilManager"));
        return;
    }

    // Validação robusta do cooldown global de 2 minutos conforme documentação
    if (!SigilManager->CanReforge())
    {
        float RemainingCooldown = SigilManager->GetReforgeTimeRemaining();
        UE_LOG(LogTemp, Warning, TEXT("OnReforgeButtonClicked: Reforge on cooldown for %f seconds"), RemainingCooldown);

        // Mostrar notificação de cooldown
        FSigilNotificationData CooldownNotification;
        CooldownNotification.Title = FText::FromString(TEXT("Re-forja em Cooldown"));
        CooldownNotification.Description = FText::FromString(FString::Printf(TEXT("Aguarde %.1f segundos"), RemainingCooldown));
        CooldownNotification.Color = FLinearColor::Red;
        CooldownNotification.Duration = 2.0f;

        // Enviar notificação para HUD se disponível
        if (UWorld* World = GetWorld())
        {
            if (APlayerController* PC = World->GetFirstPlayerController())
            {
                // Buscar HUD widget na hierarquia
                if (UUserWidget* ParentWidget = Cast<UUserWidget>(GetParent()))
                {
                    if (USigilHUDWidget* HUDWidget = Cast<USigilHUDWidget>(ParentWidget))
                    {
                        HUDWidget->ShowNotification(CooldownNotification);
                    }
                }
            }
        }
        return;
    }

    // Implementação robusta de seleção de sigilo para reforge
    TArray<int32> EligibleSlots;
    TArray<ASigilItem*> EligibleSigils;

    // Coletar todos os sígilos elegíveis para reforge
    for (int32 i = 0; i < MaxSlots; ++i)
    {
        if (ASigilItem* EquippedSigil = SigilManager->GetEquippedSigil(i))
        {
            // Verificar se o sigilo pode ser re-forjado (não está em fusão, etc.)
            // CanBeReforged() método não existe, usar verificação alternativa
            if (IsValid(EquippedSigil) && EquippedSigil->SigilData.bCanBeFused)
            {
                EligibleSlots.Add(i);
                EligibleSigils.Add(EquippedSigil);
            }
        }
    }

    if (EligibleSlots.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("OnReforgeButtonClicked: No eligible sigils for reforge"));

        // Mostrar notificação de nenhum sigilo elegível
        FSigilNotificationData NoSigilsNotification;
        NoSigilsNotification.Title = FText::FromString(TEXT("Nenhum Sígilo Elegível"));
        NoSigilsNotification.Description = FText::FromString(TEXT("Equipe sígilos para poder re-forjá-los"));
        NoSigilsNotification.Color = FLinearColor::Yellow;
        NoSigilsNotification.Duration = 3.0f;

        // Enviar notificação (mesmo código de cima, poderia ser refatorado em função)
        if (UWorld* World = GetWorld())
        {
            if (UUserWidget* ParentWidget = Cast<UUserWidget>(GetParent()))
            {
                if (USigilHUDWidget* HUDWidget = Cast<USigilHUDWidget>(ParentWidget))
                {
                    HUDWidget->ShowNotification(NoSigilsNotification);
                }
            }
        }
        return;
    }

    // Estratégia de seleção inteligente baseada na documentação
    int32 SelectedSlotIndex = -1;
    ASigilItem* SelectedSigil = nullptr;

    // Prioridade 1: Sigilo de menor raridade (mais benefício da re-forja)
    ESigilRarity LowestRarity = ESigilRarity::Legendary;
    for (int32 i = 0; i < EligibleSigils.Num(); ++i)
    {
        if (EligibleSigils[i]->SigilData.Rarity < LowestRarity)
        {
            LowestRarity = EligibleSigils[i]->SigilData.Rarity;
            SelectedSlotIndex = EligibleSlots[i];
            SelectedSigil = EligibleSigils[i];
        }
    }

    // Prioridade 2: Se todos têm mesma raridade, escolher o de menor nível
    if (SelectedSlotIndex == -1)
    {
        int32 LowestLevel = INT32_MAX;
        for (int32 i = 0; i < EligibleSigils.Num(); ++i)
        {
            if (EligibleSigils[i]->SigilData.CurrentLevel < LowestLevel)
            {
                LowestLevel = EligibleSigils[i]->SigilData.CurrentLevel;
                SelectedSlotIndex = EligibleSlots[i];
                SelectedSigil = EligibleSigils[i];
            }
        }
    }

    // Fallback: primeiro sigilo elegível
    if (SelectedSlotIndex == -1)
    {
        SelectedSlotIndex = EligibleSlots[0];
        SelectedSigil = EligibleSigils[0];
    }

    // Executar re-forja com validações
    if (SelectedSigil && SelectedSlotIndex >= 0)
    {
        UE_LOG(LogTemp, Log, TEXT("OnReforgeButtonClicked: Reforging sigil %s in slot %d"),
            *SelectedSigil->SigilData.SigilName.ToString(), SelectedSlotIndex);

        // Mostrar notificação de início de re-forja
        FSigilNotificationData ReforgeNotification;
        ReforgeNotification.Title = FText::FromString(TEXT("Re-forja Iniciada"));
        ReforgeNotification.Description = FText::FromString(FString::Printf(TEXT("Re-forjando %s"),
            *SelectedSigil->SigilData.SigilName.ToString()));
        ReforgeNotification.Color = FLinearColor::Blue;
        ReforgeNotification.Duration = 3.0f;
        ReforgeNotification.NotificationRarity = SelectedSigil->SigilData.Rarity;

        // Executar re-forja
        bool bReforgeSuccess = SigilManager->ReforgeSigil(SelectedSlotIndex);

        if (bReforgeSuccess)
        {
            // Atualizar UI
            UpdateAllSlots();
            UpdateStats();
            UpdateReforgeButton();

            UE_LOG(LogTemp, Log, TEXT("OnReforgeButtonClicked: Reforge successful for slot %d"), SelectedSlotIndex);
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("OnReforgeButtonClicked: Reforge failed for slot %d"), SelectedSlotIndex);

            // Mostrar notificação de erro
            ReforgeNotification.Title = FText::FromString(TEXT("Erro na Re-forja"));
            ReforgeNotification.Description = FText::FromString(TEXT("Falha ao re-forjar o sígilo"));
            ReforgeNotification.Color = FLinearColor::Red;
        }

        // Enviar notificação
        if (UWorld* World = GetWorld())
        {
            if (UUserWidget* ParentWidget = Cast<UUserWidget>(GetParent()))
            {
                if (USigilHUDWidget* HUDWidget = Cast<USigilHUDWidget>(ParentWidget))
                {
                    HUDWidget->ShowNotification(ReforgeNotification);
                }
            }
        }
    }
}

void USigilInventoryWidget::OnSigilEquipped(int32 SlotIndex, ASigilItem* Sigil)
{
    UpdateAllSlots();
    UpdateStats();
}

void USigilInventoryWidget::OnSigilUnequipped(int32 SlotIndex, ASigilItem* Sigil)
{
    UpdateAllSlots();
    UpdateStats();
}

void USigilInventoryWidget::OnFusionCompleted(int32 SlotIndex, ASigilItem* FusedSigil)
{
    UpdateAllSlots();
    UpdateStats();
}

void USigilInventoryWidget::OnSlotUnlockedCallback(int32 SlotIndex)
{
    UnlockSlot(SlotIndex);
}

void USigilInventoryWidget::OnStatsChanged(const FSigilSystemStats& NewStats)
{
    UE_LOG(LogTemp, Log, TEXT("OnStatsChanged: Stats updated - Total Power: %f, Equipped: %d/%d"),
        NewStats.TotalSigilPower, NewStats.TotalEquippedSigils, MaxSlots);

    UpdateStats();

    // Verificar marcos de progressão baseados nas novas estatísticas
    CheckProgressionMilestones(NewStats);
}

void USigilInventoryWidget::OnPlayerLevelChanged(int32 NewLevel, int32 OldLevel)
{
    UE_LOG(LogTemp, Log, TEXT("OnPlayerLevelChanged: Level changed from %d to %d"), OldLevel, NewLevel);

    // Verificar desbloqueio de sígilos no Level 25 conforme documentação
    if (NewLevel >= 25 && OldLevel < 25)
    {
        // Mostrar notificação de desbloqueio dos Sígilos Auracron
        FSigilNotificationData UnlockNotification;
        UnlockNotification.Title = FText::FromString(TEXT("Sígilos Auracron Desbloqueados!"));
        UnlockNotification.Description = FText::FromString(TEXT("Você atingiu o Level 25 e pode usar Sígilos Auracron"));
        UnlockNotification.Color = FLinearColor(1.0f, 0.84f, 0.0f, 1.0f); // Gold
        UnlockNotification.Duration = 5.0f;
        UnlockNotification.NotificationRarity = ESigilRarity::Legendary;

        // Enviar notificação
        if (UWorld* World = GetWorld())
        {
            if (UUserWidget* ParentWidget = Cast<UUserWidget>(GetParent()))
            {
                if (USigilHUDWidget* HUDWidget = Cast<USigilHUDWidget>(ParentWidget))
                {
                    HUDWidget->ShowNotification(UnlockNotification);
                }
            }
        }

        // Atualizar todos os slots para refletir o desbloqueio
        UpdateAllSlots();
    }

    // Verificar outros marcos de progressão
    TArray<int32> Milestones = {10, 50, 100, 200, 500}; // Conforme documentação
    for (int32 Milestone : Milestones)
    {
        if (NewLevel >= Milestone && OldLevel < Milestone)
        {
            HandleLevelMilestone(Milestone);
        }
    }
}

void USigilInventoryWidget::OnSigilSubtypeChanged(int32 SlotIndex, ESigilSubType NewSubtype, ESigilSubType OldSubtype)
{
    UE_LOG(LogTemp, Log, TEXT("OnSigilSubtypeChanged: Slot %d subtype changed from %d to %d"),
        SlotIndex, (int32)OldSubtype, (int32)NewSubtype);

    // Atualizar visual do slot específico
    if (USigilSlotWidget* SlotWidget = GetSlotWidget(SlotIndex))
    {
        SlotWidget->UpdateVisualState();
        SlotWidget->UpdateRarityIndicator();

        // Tocar efeito específico do subtipo
        switch (NewSubtype)
        {
            case ESigilSubType::Aegis:
                SlotWidget->PlayVFXEffect(ESigilSlotState::Equipped);
                break;
            case ESigilSubType::Ruin:
                SlotWidget->PlayVFXEffect(ESigilSlotState::FusionReady);
                break;
            case ESigilSubType::Vesper:
                SlotWidget->PlayVFXEffect(ESigilSlotState::Highlighted);
                break;
        }
    }

    // Mostrar notificação de mudança de subtipo
    FString SubtypeName = GetSigilSubtypeName(NewSubtype);
    FSigilNotificationData SubtypeNotification;
    SubtypeNotification.Title = FText::FromString(TEXT("Subtipo Alterado"));
    SubtypeNotification.Description = FText::FromString(FString::Printf(TEXT("Sígilo alterado para %s"), *SubtypeName));
    SubtypeNotification.Color = GetSigilSubtypeColor(NewSubtype);
    SubtypeNotification.Duration = 2.0f;

    // Enviar notificação
    if (UWorld* World = GetWorld())
    {
        if (UUserWidget* ParentWidget = Cast<UUserWidget>(GetParent()))
        {
            if (USigilHUDWidget* HUDWidget = Cast<USigilHUDWidget>(ParentWidget))
            {
                HUDWidget->ShowNotification(SubtypeNotification);
            }
        }
    }

    UpdateStats();
}

void USigilInventoryWidget::OnSigilExperienceGained(int32 SlotIndex, float ExperienceGained, float TotalExperience)
{
    UE_LOG(LogTemp, Log, TEXT("OnSigilExperienceGained: Slot %d gained %f XP (Total: %f)"),
        SlotIndex, ExperienceGained, TotalExperience);

    // Atualizar visual do slot
    if (USigilSlotWidget* SlotWidget = GetSlotWidget(SlotIndex))
    {
        // Tocar efeito de ganho de experiência
        SlotWidget->PlayVFXEffect(ESigilSlotState::Highlighted);
        SlotWidget->PlaySlotSound(ESigilSlotState::Equipped);
    }

    // Verificar se houve level up
    if (SigilManager)
    {
        if (ASigilItem* Sigil = SigilManager->GetEquippedSigil(SlotIndex))
        {
            // CalculateLevelFromExperience método não existe, usar cálculo alternativo
            int32 NewLevel = FMath::FloorToInt(TotalExperience / 100.0f) + 1; // Exemplo de cálculo
            int32 OldLevel = Sigil->SigilData.CurrentLevel;

            if (NewLevel > OldLevel)
            {
                // Mostrar notificação de level up
                FSigilNotificationData LevelUpNotification;
                LevelUpNotification.Title = FText::FromString(TEXT("Sígilo Evoluiu!"));
                LevelUpNotification.Description = FText::FromString(FString::Printf(TEXT("%s atingiu nível %d"),
                    *Sigil->SigilData.SigilName.ToString(), NewLevel));
                LevelUpNotification.Color = FLinearColor::Green;
                LevelUpNotification.Duration = 3.0f;
                LevelUpNotification.NotificationRarity = Sigil->SigilData.Rarity;

                // Enviar notificação
                if (UWorld* World = GetWorld())
                {
                    if (UUserWidget* ParentWidget = Cast<UUserWidget>(GetParent()))
                    {
                        if (USigilHUDWidget* HUDWidget = Cast<USigilHUDWidget>(ParentWidget))
                        {
                            HUDWidget->ShowNotification(LevelUpNotification);
                        }
                    }
                }
            }
        }
    }

    UpdateStats();
}

// ========================================
// NOTIFICATION WIDGET
// ========================================

USigilNotificationWidget::USigilNotificationWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    RemainingDuration = 0.0f;
    bIsShowing = false;
}

void USigilNotificationWidget::NativeConstruct()
{
    Super::NativeConstruct();
    SetVisibility(ESlateVisibility::Hidden);
}

void USigilNotificationWidget::NativeTick(const FGeometry& MyGeometry, float InDeltaTime)
{
    Super::NativeTick(MyGeometry, InDeltaTime);

    if (bIsShowing && RemainingDuration > 0.0f)
    {
        RemainingDuration -= InDeltaTime;
        
        // Atualizar barra de duração
        if (DurationBar)
        {
            float Progress = RemainingDuration / CurrentNotification.Duration;
            DurationBar->SetPercent(Progress);
        }

        // Esconder quando acabar o tempo
        if (RemainingDuration <= 0.0f)
        {
            HideNotification();
        }
    }
}

void USigilNotificationWidget::ShowNotification(const FSigilNotificationData& NotificationData)
{
    CurrentNotification = NotificationData;
    RemainingDuration = NotificationData.Duration;
    bIsShowing = true;

    // Atualizar componentes UI
    if (TitleText)
    {
        TitleText->SetText(NotificationData.Title);
    }

    if (DescriptionText)
    {
        DescriptionText->SetText(NotificationData.Description);
    }

    if (NotificationIcon && NotificationData.Icon.IsValid())
    {
        NotificationIcon->SetBrushFromTexture(NotificationData.Icon.Get());
    }

    if (NotificationBorder)
    {
        NotificationBorder->SetBrushColor(NotificationData.Color);
    }

    if (DurationBar)
    {
        DurationBar->SetPercent(1.0f);
    }

    SetVisibility(ESlateVisibility::Visible);
    OnNotificationShown(NotificationData);
}

void USigilNotificationWidget::HideNotification()
{
    bIsShowing = false;
    RemainingDuration = 0.0f;
    SetVisibility(ESlateVisibility::Hidden);
    OnNotificationHidden();
}

// ========================================
// HUD WIDGET
// ========================================

USigilHUDWidget::USigilHUDWidget(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    NotificationWidgetClass = USigilNotificationWidget::StaticClass();
    MaxNotifications = 5;
}

void USigilHUDWidget::NativeConstruct()
{
    Super::NativeConstruct();
}

void USigilHUDWidget::InitializeHUD(USigilManagerComponent* SigilManager)
{
    if (InventoryWidget)
    {
        InventoryWidget->InitializeInventory(SigilManager);
    }

    OnHUDInitialized();
}

void USigilHUDWidget::ShowNotification(const FSigilNotificationData& NotificationData)
{
    if (!NotificationWidgetClass || !NotificationsContainer)
    {
        return;
    }

    // Remover notificações antigas se necessário
    while (ActiveNotifications.Num() >= MaxNotifications)
    {
        RemoveOldestNotification();
    }

    // Criar nova notificação
    USigilNotificationWidget* NotificationWidget = CreateWidget<USigilNotificationWidget>(GetWorld(), NotificationWidgetClass);
    if (NotificationWidget)
    {
        ActiveNotifications.Add(NotificationWidget);
        
        UVerticalBoxSlot* NotificationSlot = NotificationsContainer->AddChildToVerticalBox(NotificationWidget);
        if (NotificationSlot)
        {
            NotificationSlot->SetSize(FSlateChildSize(ESlateSizeRule::Automatic));
            NotificationSlot->SetPadding(FMargin(0.0f, 2.0f));
        }

        NotificationWidget->ShowNotification(NotificationData);
    }
}

void USigilHUDWidget::ClearAllNotifications()
{
    for (USigilNotificationWidget* Notification : ActiveNotifications)
    {
        if (Notification)
        {
            Notification->HideNotification();
            Notification->RemoveFromParent();
        }
    }
    ActiveNotifications.Empty();
}

void USigilHUDWidget::RemoveOldestNotification()
{
    if (ActiveNotifications.Num() > 0)
    {
        USigilNotificationWidget* OldestNotification = ActiveNotifications[0];
        if (OldestNotification)
        {
            OldestNotification->HideNotification();
            OldestNotification->RemoveFromParent();
        }
        ActiveNotifications.RemoveAt(0);

        UE_LOG(LogTemp, Verbose, TEXT("RemoveOldestNotification: Removed oldest notification, %d remaining"),
            ActiveNotifications.Num());
    }
}

// ========================================
// FUNÇÕES HELPER ADICIONAIS
// ========================================

void USigilInventoryWidget::CheckProgressionMilestones(const FSigilSystemStats& Stats)
{
    // Verificar marcos de progressão baseados nas estatísticas
    static float LastTotalPower = 0.0f;
    static int32 LastEquippedCount = 0;

    // Marco de poder total
    if (Stats.TotalSigilPower >= 1000.0f && LastTotalPower < 1000.0f)
    {
        ShowMilestoneNotification(TEXT("Mestre dos Sígilos"), TEXT("Poder total atingiu 1000!"), FLinearColor(1.0f, 0.84f, 0.0f, 1.0f)); // Gold
    }
    else if (Stats.TotalSigilPower >= 500.0f && LastTotalPower < 500.0f)
    {
        ShowMilestoneNotification(TEXT("Usuário Avançado"), TEXT("Poder total atingiu 500!"), FLinearColor::Blue);
    }

    // Marco de slots equipados
    if (Stats.TotalEquippedSigils == MaxSlots && LastEquippedCount < MaxSlots)
    {
        ShowMilestoneNotification(TEXT("Arsenal Completo"), TEXT("Todos os slots equipados!"), FLinearColor::Green);
    }

    LastTotalPower = Stats.TotalSigilPower;
    LastEquippedCount = Stats.TotalEquippedSigils;
}

void USigilInventoryWidget::HandleLevelMilestone(int32 Milestone)
{
    FString Title;
    FString Description;
    FLinearColor Color = FLinearColor::Blue;

    switch (Milestone)
    {
        case 10:
            Title = TEXT("Modo Ranqueado Desbloqueado");
            Description = TEXT("Você pode jogar partidas ranqueadas!");
            break;
        case 25:
            Title = TEXT("Sígilos Auracron Desbloqueados");
            Description = TEXT("Sistema de fusão disponível!");
            Color = FLinearColor::Gold;
            break;
        case 50:
            Title = TEXT("Maestria de Realm");
            Description = TEXT("Rastreamento de maestria ativado!");
            break;
        case 100:
            Title = TEXT("Lobbies Customizados");
            Description = TEXT("Crie suas próprias partidas!");
            break;
        case 200:
            Title = TEXT("Beta Tester");
            Description = TEXT("Acesso a recursos beta!");
            Color = FLinearColor::Purple;
            break;
        case 500:
            Title = TEXT("Status Lendário");
            Description = TEXT("Máximo nível atingido!");
            Color = FLinearColor::Gold;
            break;
        default:
            return;
    }

    ShowMilestoneNotification(Title, Description, Color);
}

void USigilInventoryWidget::ShowMilestoneNotification(const FString& Title, const FString& Description, const FLinearColor& Color)
{
    FSigilNotificationData MilestoneNotification;
    MilestoneNotification.Title = FText::FromString(Title);
    MilestoneNotification.Description = FText::FromString(Description);
    MilestoneNotification.Color = Color;
    MilestoneNotification.Duration = 5.0f;
    MilestoneNotification.NotificationRarity = ESigilRarity::Legendary;

    // Enviar notificação
    if (UWorld* World = GetWorld())
    {
        if (UUserWidget* ParentWidget = GetParent())
        {
            if (USigilHUDWidget* HUDWidget = Cast<USigilHUDWidget>(ParentWidget))
            {
                HUDWidget->ShowNotification(MilestoneNotification);
            }
        }
    }
}

FString USigilInventoryWidget::GetSigilSubtypeName(ESigilSubType Subtype) const
{
    switch (Subtype)
    {
        case ESigilSubType::Aegis:
            return TEXT("Aegis (Tanque)");
        case ESigilSubType::Ruin:
            return TEXT("Ruin (Dano)");
        case ESigilSubType::Vesper:
            return TEXT("Vesper (Utilidade)");
        default:
            return TEXT("Desconhecido");
    }
}

FLinearColor USigilInventoryWidget::GetSigilSubtypeColor(ESigilSubType Subtype) const
{
    switch (Subtype)
    {
        case ESigilSubType::Aegis:
            return FLinearColor::Blue;   // Cor de tanque
        case ESigilSubType::Ruin:
            return FLinearColor::Red;    // Cor de dano
        case ESigilSubType::Vesper:
            return FLinearColor::Green;  // Cor de utilidade
        default:
            return FLinearColor::White;
    }
}