// SigilVFXManager.cpp
// AURACRON - Sistema de Sígilos
// Implementação do gerenciador de efeitos visuais Niagara com pooling
// APIs UE 5.6: NiagaraComponent.h, NiagaraSystem.h, NiagaraFunctionLibrary.h

#include "VFX/SigilVFXManager.h"
#include "Sigils/SigilItem.h"
#include "NiagaraFunctionLibrary.h"
#include "NiagaraDataInterfaceArrayFunctionLibrary.h"
#include "Components/SceneComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/Engine.h"
#include "TimerManager.h"
#include "GameplayTagsManager.h"
#include "Logging/LogMacros.h"
#include "Engine/AssetManager.h"
#include "Engine/StreamableManager.h"

// ========================================
// CONSTRUCTOR & INITIALIZATION
// ========================================

USigilVFXManager::USigilVFXManager()
{
    PrimaryComponentTick.bCanEverTick = true;
    PrimaryComponentTick.TickInterval = 0.1f; // Tick a cada 100ms para otimização
    
    NextInstanceID = 1;
    CleanupTimer = 0.0f;
    OptimizationTimer = 0.0f;
    bIsInitialized = false;
    LastOptimizationTime = 0.0f;
    
    // Configurações padrão
    MaxSimultaneousEffects = 100;
    MaxEffectsPerActor = 10;
    CleanupInterval = 5.0f;
    bUsePooling = true;
    bAutoOptimize = true;
    
    // Inicializar configurações padrão
    InitializeDefaultConfigs();
}

void USigilVFXManager::BeginPlay()
{
    Super::BeginPlay();
    InitializeVFXManager();
}

void USigilVFXManager::EndPlay(const EEndPlayReason::Type EndPlayReason)
{
    ShutdownVFXManager();
    Super::EndPlay(EndPlayReason);
}

void USigilVFXManager::TickComponent(float DeltaTime, ELevelTick TickType, FActorComponentTickFunction* ThisTickFunction)
{
    Super::TickComponent(DeltaTime, TickType, ThisTickFunction);
    
    if (!bIsInitialized)
    {
        return;
    }
    
    // Atualizar instâncias ativas
    UpdateActiveInstances(DeltaTime);
    
    // Limpeza automática robusta
    CleanupTimer += DeltaTime;
    if (CleanupTimer >= CleanupInterval)
    {
        PerformAutomaticCleanup();
        CleanupExpiredVFX(); // Adicionar limpeza de VFX expirados
        CleanupTimer = 0.0f;
    }
    
    // Otimização automática
    if (bAutoOptimize)
    {
        float CurrentTime = GetWorld()->GetTimeSeconds();
        if (CurrentTime - LastOptimizationTime >= AUTO_OPTIMIZATION_INTERVAL)
        {
            OptimizePools();
            LastOptimizationTime = CurrentTime;
        }
    }
    
    // Atualizar estatísticas
    UpdateStats();
}

void USigilVFXManager::InitializeVFXManager()
{
    if (bIsInitialized)
    {
        return;
    }
    
    // Limpar dados existentes
    ActiveVFXInstances.Empty();
    ComponentPools.Empty();
    
    // Resetar contadores
    NextInstanceID = 1;
    CleanupTimer = 0.0f;
    LastOptimizationTime = GetWorld()->GetTimeSeconds();
    
    // Carregar sistemas VFX padrão assincronamente
    LoadDefaultVFXSystems();

    // Inicializar pools para sistemas comuns
    PreloadCommonVFXPools();

    // Validar todas as configurações
    ValidateAllVFXConfigs();

    bIsInitialized = true;

    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Initialized successfully"));
}

void USigilVFXManager::ShutdownVFXManager()
{
    if (!bIsInitialized)
    {
        return;
    }
    
    // Parar todos os efeitos ativos
    ClearAllVFX();
    
    // Limpar pools
    ForceCleanupAllPools();
    
    bIsInitialized = false;
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Shutdown completed"));
}

void USigilVFXManager::InitializeDefaultConfigs()
{
    // Configuração para Equip
    FSigilVFXConfig EquipConfig;
    EquipConfig.Duration = 2.0f;
    EquipConfig.Priority = ESigilVFXPriority::Medium;
    EquipConfig.bShouldLoop = false;
    EquipConfig.bAttachToActor = true;
    EquipConfig.Scale = FVector(1.0f, 1.0f, 1.0f);
    EquipConfig.Color = FLinearColor::Green;
    DefaultVFXConfigs.Add(ESigilVFXType::Equip, EquipConfig);
    
    // Configuração para Unequip
    FSigilVFXConfig UnequipConfig;
    UnequipConfig.Duration = 1.5f;
    UnequipConfig.Priority = ESigilVFXPriority::Low;
    UnequipConfig.bShouldLoop = false;
    UnequipConfig.bAttachToActor = true;
    UnequipConfig.Scale = FVector(0.8f, 0.8f, 0.8f);
    UnequipConfig.Color = FLinearColor::Red;
    DefaultVFXConfigs.Add(ESigilVFXType::Unequip, UnequipConfig);
    
    // Configuração para Fusion Start
    FSigilVFXConfig FusionStartConfig;
    FusionStartConfig.Duration = 3.0f;
    FusionStartConfig.Priority = ESigilVFXPriority::High;
    FusionStartConfig.bShouldLoop = true;
    FusionStartConfig.bAttachToActor = true;
    FusionStartConfig.Scale = FVector(1.2f, 1.2f, 1.2f);
    FusionStartConfig.Color = FLinearColor::Yellow;
    DefaultVFXConfigs.Add(ESigilVFXType::FusionStart, FusionStartConfig);
    
    // Configuração para Fusion Complete
    FSigilVFXConfig FusionCompleteConfig;
    FusionCompleteConfig.Duration = 4.0f;
    FusionCompleteConfig.Priority = ESigilVFXPriority::High;
    FusionCompleteConfig.bShouldLoop = false;
    FusionCompleteConfig.bAttachToActor = true;
    FusionCompleteConfig.Scale = FVector(1.5f, 1.5f, 1.5f);
    FusionCompleteConfig.Color = FLinearColor(1.0f, 0.84f, 0.0f, 1.0f); // Gold
    DefaultVFXConfigs.Add(ESigilVFXType::FusionComplete, FusionCompleteConfig);
    
    // Configuração para Spectral Aura
    FSigilVFXConfig SpectralAuraConfig;
    SpectralAuraConfig.Duration = 0.0f; // Permanente até ser removido
    SpectralAuraConfig.Priority = ESigilVFXPriority::Medium;
    SpectralAuraConfig.bShouldLoop = true;
    SpectralAuraConfig.bAttachToActor = true;
    SpectralAuraConfig.Scale = FVector(2.0f, 2.0f, 2.0f);
    SpectralAuraConfig.Color = FLinearColor(0.0f, 1.0f, 1.0f, 1.0f); // Cyan
    DefaultVFXConfigs.Add(ESigilVFXType::SpectralAura, SpectralAuraConfig);
    
    // Configuração para Team Fight
    FSigilVFXConfig TeamFightConfig;
    TeamFightConfig.Duration = 5.0f;
    TeamFightConfig.Priority = ESigilVFXPriority::High;
    TeamFightConfig.bShouldLoop = true;
    TeamFightConfig.bAttachToActor = true;
    TeamFightConfig.Scale = FVector(1.8f, 1.8f, 1.8f);
    TeamFightConfig.Color = FLinearColor(1.0f, 0.5f, 0.0f, 1.0f); // Orange
    DefaultVFXConfigs.Add(ESigilVFXType::TeamFight, TeamFightConfig);
    
    // Configuração para Critical
    FSigilVFXConfig CriticalConfig;
    CriticalConfig.Duration = 1.0f;
    CriticalConfig.Priority = ESigilVFXPriority::High;
    CriticalConfig.bShouldLoop = false;
    CriticalConfig.bAttachToActor = false;
    CriticalConfig.Scale = FVector(1.3f, 1.3f, 1.3f);
    CriticalConfig.Color = FLinearColor::Red;
    DefaultVFXConfigs.Add(ESigilVFXType::Critical, CriticalConfig);
    
    // Configurações por raridade
    InitializeRarityConfigs();
}

void USigilVFXManager::InitializeRarityConfigs()
{
    // Common
    FSigilVFXConfig CommonConfig;
    CommonConfig.Scale = FVector(0.8f, 0.8f, 0.8f);
    CommonConfig.Color = FLinearColor::White;
    RarityVFXConfigs.Add(ESigilRarity::Common, CommonConfig);
    
    // Common é o nível básico de raridade
    
    // Rare
    FSigilVFXConfig RareConfig;
    RareConfig.Scale = FVector(1.2f, 1.2f, 1.2f);
    RareConfig.Color = FLinearColor::Blue;
    RarityVFXConfigs.Add(ESigilRarity::Rare, RareConfig);
    
    // Epic
    FSigilVFXConfig EpicConfig;
    EpicConfig.Scale = FVector(1.4f, 1.4f, 1.4f);
    EpicConfig.Color = FLinearColor(0.5f, 0.0f, 1.0f, 1.0f); // Purple
    RarityVFXConfigs.Add(ESigilRarity::Epic, EpicConfig);
    
    // Legendary
    FSigilVFXConfig LegendaryConfig;
    LegendaryConfig.Scale = FVector(1.6f, 1.6f, 1.6f);
    LegendaryConfig.Color = FLinearColor(1.0f, 0.5f, 0.0f, 1.0f); // Orange
    RarityVFXConfigs.Add(ESigilRarity::Legendary, LegendaryConfig);
    
    // Legendary é o nível máximo de raridade
}

void USigilVFXManager::PreloadCommonVFXPools()
{
    if (!bIsInitialized)
    {
        UE_LOG(LogTemp, Warning, TEXT("SigilVFXManager: Cannot preload pools - manager not initialized"));
        return;
    }

    // Verificar se o pooling está habilitado
    if (!bUsePooling)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Pooling disabled, skipping preload"));
        return;
    }

    int32 TotalPreloadedSystems = 0;
    int32 TotalPreloadedComponents = 0;

    // Pré-carregar pools baseados nas configurações padrão existentes
    for (const auto& ConfigPair : DefaultVFXConfigs)
    {
        const ESigilVFXType VFXType = ConfigPair.Key;
        const FSigilVFXConfig& Config = ConfigPair.Value;
        
        // Verificar se há um sistema Niagara válido configurado
        if (Config.NiagaraSystem.IsValid())
        {
            UNiagaraSystem* NiagaraSystem = Config.NiagaraSystem.LoadSynchronous();
            if (IsValid(NiagaraSystem))
            {
                // Determinar tamanho do pool baseado no tipo de VFX
                int32 PoolSize = GetOptimalPoolSize(VFXType);
                
                // Pré-carregar o pool
                PreloadVFXPool(NiagaraSystem, PoolSize);
                
                TotalPreloadedSystems++;
                TotalPreloadedComponents += PoolSize;
                
                UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Preloaded pool for %s (Type: %d) with %d components"),
                    *NiagaraSystem->GetName(), (int32)VFXType, PoolSize);
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("SigilVFXManager: Failed to load Niagara system for VFX type %d"), (int32)VFXType);
            }
        }
    }

    // Pré-carregar pools para configurações de raridade
    for (const auto& RarityPair : RarityVFXConfigs)
    {
        const ESigilRarity Rarity = RarityPair.Key;
        const FSigilVFXConfig& Config = RarityPair.Value;
        
        if (Config.NiagaraSystem.IsValid())
        {
            UNiagaraSystem* NiagaraSystem = Config.NiagaraSystem.LoadSynchronous();
            if (IsValid(NiagaraSystem))
            {
                // Pool menor para efeitos de raridade (menos frequentes)
                int32 PoolSize = GetRarityPoolSize(Rarity);
                
                PreloadVFXPool(NiagaraSystem, PoolSize);
                
                TotalPreloadedSystems++;
                TotalPreloadedComponents += PoolSize;
                
                UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Preloaded rarity pool for %s (Rarity: %d) with %d components"),
                    *NiagaraSystem->GetName(), (int32)Rarity, PoolSize);
            }
        }
    }

    // Otimizar pools após preload
    if (bAutoOptimize)
    {
        OptimizePools();
    }

    // Atualizar estatísticas
    UpdateStats();

    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Preload completed - %d systems, %d total components"),
        TotalPreloadedSystems, TotalPreloadedComponents);
}

// ========================================
// CORE VFX FUNCTIONS
// ========================================

int32 USigilVFXManager::PlayVFXEffect(ESigilVFXType VFXType, AActor* TargetActor, const FSigilVFXConfig& Config)
{
    if (!bIsInitialized || !TargetActor || VFXType == ESigilVFXType::None)
    {
        return -1;
    }
    
    // Verificar se pode criar novo efeito
    if (!CanCreateNewEffect(TargetActor))
    {
        // Remover efeitos de baixa prioridade se necessário
        RemoveLowPriorityEffects(1);
        
        if (!CanCreateNewEffect(TargetActor))
        {
            UE_LOG(LogTemp, Warning, TEXT("SigilVFXManager: Cannot create new effect, limits reached"));
            return -1;
        }
    }
    
    // Validar configuração
    if (!ValidateVFXConfig(Config))
    {
        UE_LOG(LogTemp, Warning, TEXT("SigilVFXManager: Invalid VFX config"));
        return -1;
    }
    
    // Criar nova instância
    FSigilVFXInstance NewInstance = CreateVFXInstance(VFXType, TargetActor, Config);
    if (!NewInstance.bIsActive)
    {
        return -1;
    }
    
    // Adicionar à lista de instâncias ativas
    ActiveVFXInstances.Add(NewInstance.InstanceID, NewInstance);
    
    // Disparar evento
    OnVFXStarted.Broadcast(VFXType, NewInstance.InstanceID);
    
    UE_LOG(LogTemp, Verbose, TEXT("SigilVFXManager: Started VFX %d of type %d"), NewInstance.InstanceID, (int32)VFXType);
    
    return NewInstance.InstanceID;
}

void USigilVFXManager::DebugSpawnTestVFX(ESigilVFXType VFXType, AActor* TargetActor)
{
    if (!TargetActor)
    {
        TargetActor = GetOwner();
    }
    
    if (!TargetActor)
    {
        UE_LOG(LogTemp, Warning, TEXT("SigilVFXManager: No target actor for debug VFX"));
        return;
    }
    
    FSigilVFXConfig Config = GetDefaultVFXConfig(VFXType);
    Config.Duration = 5.0f; // Duração fixa para debug
    
    int32 InstanceID = PlayVFXEffect(VFXType, TargetActor, Config);
    
    if (InstanceID != -1)
    {
        UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Debug VFX spawned with ID %d"), InstanceID);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("SigilVFXManager: Failed to spawn debug VFX"));
    }
}

void USigilVFXManager::DebugClearAllVFX()
{
    int32 ClearedCount = ActiveVFXInstances.Num();
    ClearAllVFX();
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Debug cleared %d VFX effects"), ClearedCount);
}

void USigilVFXManager::DebugTogglePooling()
{
    bUsePooling = !bUsePooling;
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Pooling %s"), 
        bUsePooling ? TEXT("ENABLED") : TEXT("DISABLED"));
    
    if (!bUsePooling)
    {
        // Se desabilitou pooling, limpar todos os pools
        ForceCleanupAllPools();
    }
}

void USigilVFXManager::DebugSetMaxEffects(int32 NewMaxEffects)
{
    int32 OldMax = MaxSimultaneousEffects;
    MaxSimultaneousEffects = FMath::Max(1, NewMaxEffects);
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Max effects changed from %d to %d"), 
        OldMax, MaxSimultaneousEffects);
    
    // Se o novo limite é menor, remover efeitos em excesso
    if (ActiveVFXInstances.Num() > MaxSimultaneousEffects)
    {
        int32 EffectsToRemove = ActiveVFXInstances.Num() - MaxSimultaneousEffects;
        RemoveLowPriorityEffects(EffectsToRemove);
    }
}

// ========================================
// CONSOLE COMMANDS
// ========================================

// Implementação de console commands seria feita aqui
// Exemplo: UFUNCTION(CallInEditor = true, Category = "Debug")
// void ExecuteDebugCommand(const FString& Command);

// ========================================
// CONSTANTS IMPLEMENTATION
// ========================================
// Constantes são definidas como static constexpr no header

bool USigilVFXManager::StopVFXEffect(int32 InstanceID)
{
    if (!ActiveVFXInstances.Contains(InstanceID))
    {
        return false;
    }
    
    FSigilVFXInstance& Instance = ActiveVFXInstances[InstanceID];
    
    // Parar o componente Niagara
    if (Instance.NiagaraComponent && IsValid(Instance.NiagaraComponent))
    {
        Instance.NiagaraComponent->Deactivate();
        
        // Retornar ao pool se estiver usando pooling
        if (bUsePooling && Instance.Config.NiagaraSystem.IsValid())
        {
            ReturnComponentToPool(Instance.NiagaraComponent, Instance.Config.NiagaraSystem.Get());
        }
        else
        {
            Instance.NiagaraComponent->DestroyComponent();
        }
    }
    
    // Disparar evento
    OnVFXCompleted.Broadcast(Instance.VFXType, InstanceID);
    
    // Remover da lista
    ActiveVFXInstances.Remove(InstanceID);
    
    UE_LOG(LogTemp, Verbose, TEXT("SigilVFXManager: Stopped VFX %d"), InstanceID);
    
    return true;
}

void USigilVFXManager::StopAllVFXForActor(AActor* TargetActor)
{
    if (!TargetActor)
    {
        return;
    }
    
    TArray<int32> InstancesToRemove;
    
    for (const auto& Pair : ActiveVFXInstances)
    {
        const FSigilVFXInstance& Instance = Pair.Value;
        if (Instance.OwnerActor.Get() == TargetActor)
        {
            InstancesToRemove.Add(Pair.Key);
        }
    }
    
    for (int32 InstanceID : InstancesToRemove)
    {
        StopVFXEffect(InstanceID);
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Stopped %d VFX effects for actor %s"), 
        InstancesToRemove.Num(), *TargetActor->GetName());
}

void USigilVFXManager::StopAllVFXOfType(ESigilVFXType VFXType)
{
    TArray<int32> InstancesToRemove;
    
    for (const auto& Pair : ActiveVFXInstances)
    {
        const FSigilVFXInstance& Instance = Pair.Value;
        if (Instance.VFXType == VFXType)
        {
            InstancesToRemove.Add(Pair.Key);
        }
    }
    
    for (int32 InstanceID : InstancesToRemove)
    {
        StopVFXEffect(InstanceID);
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Stopped %d VFX effects of type %d"), 
        InstancesToRemove.Num(), (int32)VFXType);
}

void USigilVFXManager::ClearAllVFX()
{
    TArray<int32> AllInstanceIDs;
    ActiveVFXInstances.GetKeys(AllInstanceIDs);
    
    for (int32 InstanceID : AllInstanceIDs)
    {
        StopVFXEffect(InstanceID);
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Cleared all VFX effects (%d total)"), AllInstanceIDs.Num());
}

// ========================================
// SIGIL-SPECIFIC FUNCTIONS
// ========================================

int32 USigilVFXManager::PlaySigilEquipVFX(ASigilItem* Sigil, AActor* TargetActor)
{
    if (!Sigil || !TargetActor)
    {
        return -1;
    }
    
    FSigilVFXConfig Config = GetSigilBasedConfig(Sigil, ESigilVFXType::Equip);
    return PlayVFXEffect(ESigilVFXType::Equip, TargetActor, Config);
}

int32 USigilVFXManager::PlaySigilUnequipVFX(ASigilItem* Sigil, AActor* TargetActor)
{
    if (!Sigil || !TargetActor)
    {
        return -1;
    }
    
    FSigilVFXConfig Config = GetSigilBasedConfig(Sigil, ESigilVFXType::Unequip);
    return PlayVFXEffect(ESigilVFXType::Unequip, TargetActor, Config);
}

int32 USigilVFXManager::PlaySigilFusionVFX(ASigilItem* Sigil, AActor* TargetActor, bool bIsComplete)
{
    if (!Sigil || !TargetActor)
    {
        return -1;
    }
    
    ESigilVFXType VFXType = bIsComplete ? ESigilVFXType::FusionComplete : ESigilVFXType::FusionStart;
    FSigilVFXConfig Config = GetSigilBasedConfig(Sigil, VFXType);
    
    // Configurações especiais para fusão
    if (bIsComplete)
    {
        Config.Duration = 4.0f;
        Config.Scale *= 1.5f;
        Config.Priority = ESigilVFXPriority::Critical;
    }
    else
    {
        Config.Duration = 360.0f; // 6 minutos para fusão automática
        Config.bShouldLoop = true;
    }
    
    return PlayVFXEffect(VFXType, TargetActor, Config);
}

int32 USigilVFXManager::PlaySigilReforgeVFX(ASigilItem* Sigil, AActor* TargetActor)
{
    if (!Sigil || !TargetActor)
    {
        return -1;
    }
    
    FSigilVFXConfig Config = GetSigilBasedConfig(Sigil, ESigilVFXType::Reforge);
    Config.Duration = 3.0f;
    Config.Priority = ESigilVFXPriority::High;
    Config.Scale *= 1.3f;
    
    return PlayVFXEffect(ESigilVFXType::Reforge, TargetActor, Config);
}

int32 USigilVFXManager::PlaySpectralAuraVFX(AActor* TargetActor, ESigilRarity Rarity)
{
    if (!TargetActor)
    {
        return -1;
    }
    
    FSigilVFXConfig Config = GetDefaultVFXConfig(ESigilVFXType::SpectralAura);
    
    // Aplicar modificações baseadas na raridade
    if (RarityVFXConfigs.Contains(Rarity))
    {
        const FSigilVFXConfig& RarityConfig = RarityVFXConfigs[Rarity];
        Config.Scale = RarityConfig.Scale;
        Config.Color = RarityConfig.Color;
    }
    
    // Aura é permanente até ser removida
    Config.Duration = 0.0f;
    Config.bShouldLoop = true;
    
    return PlayVFXEffect(ESigilVFXType::SpectralAura, TargetActor, Config);
}

// ========================================
// MOBA-SPECIFIC FUNCTIONS
// ========================================

int32 USigilVFXManager::PlayTeamFightVFX(AActor* TargetActor, int32 TeamID)
{
    if (!TargetActor)
    {
        return -1;
    }
    
    FSigilVFXConfig Config = GetDefaultVFXConfig(ESigilVFXType::TeamFight);
    
    // Cor baseada no time
    if (TeamID == 0)
    {
        Config.Color = FLinearColor::Blue; // Time Azul
    }
    else if (TeamID == 1)
    {
        Config.Color = FLinearColor::Red; // Time Vermelho
    }
    
    Config.Duration = 10.0f; // Duração do team fight
    Config.Priority = ESigilVFXPriority::High;
    
    return PlayVFXEffect(ESigilVFXType::TeamFight, TargetActor, Config);
}

int32 USigilVFXManager::PlayObjectiveVFX(AActor* TargetActor, FGameplayTag ObjectiveTag)
{
    if (!TargetActor)
    {
        return -1;
    }
    
    FSigilVFXConfig Config = GetDefaultVFXConfig(ESigilVFXType::Objective);
    
    // Configurações baseadas no tipo de objetivo
    if (ObjectiveTag.MatchesTag(FGameplayTag::RequestGameplayTag("MOBA.Objective.Dragon")))
    {
        Config.Color = FLinearColor(1.0f, 0.5f, 0.0f, 1.0f); // Laranja para dragão
        Config.Scale *= 1.5f;
    }
    else if (ObjectiveTag.MatchesTag(FGameplayTag::RequestGameplayTag("MOBA.Objective.Baron")))
    {
        Config.Color = FLinearColor(0.5f, 0.0f, 1.0f, 1.0f); // Roxo para baron
        Config.Scale *= 2.0f;
    }
    else if (ObjectiveTag.MatchesTag(FGameplayTag::RequestGameplayTag("MOBA.Objective.Tower")))
    {
        Config.Color = FLinearColor::Yellow; // Amarelo para torre
        Config.Scale *= 1.2f;
    }
    
    Config.Duration = 5.0f;
    Config.Priority = ESigilVFXPriority::High;
    
    return PlayVFXEffect(ESigilVFXType::Objective, TargetActor, Config);
}

int32 USigilVFXManager::PlayCriticalVFX(AActor* TargetActor, float Damage)
{
    if (!TargetActor)
    {
        return -1;
    }
    
    FSigilVFXConfig Config = GetDefaultVFXConfig(ESigilVFXType::Critical);
    
    // Escala baseada no dano
    float DamageScale = FMath::Clamp(Damage / 1000.0f, 0.5f, 3.0f);
    Config.Scale *= DamageScale;
    
    // Cor mais intensa para danos maiores
    float Intensity = FMath::Clamp(Damage / 2000.0f, 0.5f, 1.0f);
    Config.Color = FLinearColor(1.0f, 1.0f - Intensity, 1.0f - Intensity, 1.0f);
    
    Config.Duration = 1.5f;
    Config.Priority = ESigilVFXPriority::High;
    
    return PlayVFXEffect(ESigilVFXType::Critical, TargetActor, Config);
}

// ========================================
// QUERY FUNCTIONS
// ========================================

bool USigilVFXManager::IsVFXActive(int32 InstanceID) const
{
    return ActiveVFXInstances.Contains(InstanceID);
}

FSigilVFXInstance USigilVFXManager::GetVFXInstance(int32 InstanceID) const
{
    if (ActiveVFXInstances.Contains(InstanceID))
    {
        return ActiveVFXInstances[InstanceID];
    }
    return FSigilVFXInstance();
}

TArray<FSigilVFXInstance> USigilVFXManager::GetActiveVFXForActor(AActor* TargetActor) const
{
    TArray<FSigilVFXInstance> Result;
    
    if (!TargetActor)
    {
        return Result;
    }
    
    for (const auto& Pair : ActiveVFXInstances)
    {
        const FSigilVFXInstance& Instance = Pair.Value;
        if (Instance.OwnerActor.Get() == TargetActor)
        {
            Result.Add(Instance);
        }
    }
    
    return Result;
}

FSigilVFXStats USigilVFXManager::GetVFXStats() const
{
    return CurrentStats;
}

FSigilVFXConfig USigilVFXManager::GetDefaultVFXConfig(ESigilVFXType VFXType) const
{
    if (DefaultVFXConfigs.Contains(VFXType))
    {
        return DefaultVFXConfigs[VFXType];
    }
    return FSigilVFXConfig();
}

// ========================================
// POOL MANAGEMENT
// ========================================

void USigilVFXManager::PreloadVFXPool(UNiagaraSystem* NiagaraSystem, int32 PoolSize)
{
    if (!NiagaraSystem || PoolSize <= 0)
    {
        return;
    }
    
    TSoftObjectPtr<UNiagaraSystem> SystemPtr(NiagaraSystem);
    
    if (!ComponentPools.Contains(SystemPtr))
    {
        CreatePool(NiagaraSystem, PoolSize);
    }
    else
    {
        // Expandir pool existente se necessário
        FSigilVFXPool& Pool = ComponentPools[SystemPtr];
        int32 CurrentSize = Pool.AvailableComponents.Num() + Pool.ActiveComponents.Num();
        if (CurrentSize < PoolSize)
        {
            ExpandPool(NiagaraSystem, PoolSize - CurrentSize);
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Preloaded pool for %s with %d components"), 
        *NiagaraSystem->GetName(), PoolSize);
}

void USigilVFXManager::CleanupUnusedPools()
{
    TArray<TSoftObjectPtr<UNiagaraSystem>> PoolsToRemove;
    
    for (auto& Pair : ComponentPools)
    {
        FSigilVFXPool& Pool = Pair.Value;
        
        // Se não há componentes ativos e o pool está vazio há muito tempo
        if (Pool.ActiveComponents.Num() == 0 && Pool.AvailableComponents.Num() > Pool.InitialPoolSize)
        {
            // Reduzir o pool ao tamanho inicial
            int32 ComponentsToRemove = Pool.AvailableComponents.Num() - Pool.InitialPoolSize;
            for (int32 i = 0; i < ComponentsToRemove; ++i)
            {
                if (Pool.AvailableComponents.Num() > 0)
                {
                    UNiagaraComponent* Component = Pool.AvailableComponents.Pop();
                    if (IsValid(Component))
                    {
                        Component->DestroyComponent();
                    }
                }
            }
        }
        
        // Marcar pools completamente vazios para remoção
        if (Pool.ActiveComponents.Num() == 0 && Pool.AvailableComponents.Num() == 0)
        {
            PoolsToRemove.Add(Pair.Key);
        }
    }
    
    // Remover pools vazios
    for (const TSoftObjectPtr<UNiagaraSystem>& SystemPtr : PoolsToRemove)
    {
        ComponentPools.Remove(SystemPtr);
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Cleaned up %d unused pools"), PoolsToRemove.Num());
}

void USigilVFXManager::OptimizePools()
{
    for (auto& Pair : ComponentPools)
    {
        FSigilVFXPool& Pool = Pair.Value;
        
        // Remover componentes inválidos
        Pool.AvailableComponents.RemoveAll([](const TObjectPtr<UNiagaraComponent>& Component) {
            return !IsValid(Component);
        });
        
        Pool.ActiveComponents.RemoveAll([](const TObjectPtr<UNiagaraComponent>& Component) {
            return !IsValid(Component);
        });
        
        // Ajustar tamanho do pool baseado no uso
        int32 TotalComponents = Pool.AvailableComponents.Num() + Pool.ActiveComponents.Num();
        int32 OptimalSize = FMath::Max(Pool.ActiveComponents.Num() * 2, Pool.InitialPoolSize);
        OptimalSize = FMath::Min(OptimalSize, Pool.MaxPoolSize);
        
        if (TotalComponents < OptimalSize)
        {
            // Expandir pool
            ExpandPool(Pair.Key.Get(), OptimalSize - TotalComponents);
        }
        else if (Pool.AvailableComponents.Num() > OptimalSize)
        {
            // Reduzir pool
            int32 ComponentsToRemove = Pool.AvailableComponents.Num() - OptimalSize;
            for (int32 i = 0; i < ComponentsToRemove; ++i)
            {
                if (Pool.AvailableComponents.Num() > 0)
                {
                    UNiagaraComponent* Component = Pool.AvailableComponents.Pop();
                    if (IsValid(Component))
                    {
                        Component->DestroyComponent();
                    }
                }
            }
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Optimized %d pools"), ComponentPools.Num());
}

// ========================================
// INTERNAL FUNCTIONS
// ========================================

FSigilVFXInstance USigilVFXManager::CreateVFXInstance(ESigilVFXType VFXType, AActor* TargetActor, const FSigilVFXConfig& Config)
{
    FSigilVFXInstance Instance;
    Instance.VFXType = VFXType;
    Instance.Config = Config;
    Instance.OwnerActor = TargetActor;
    Instance.InstanceID = NextInstanceID++;
    Instance.CreationTime = GetWorld()->GetTimeSeconds();
    Instance.RemainingTime = Config.Duration;
    
    // Obter sistema Niagara
    UNiagaraSystem* NiagaraSystem = Config.NiagaraSystem.Get();
    if (!NiagaraSystem)
    {
        // Usar sistema padrão se não especificado
        NiagaraSystem = GetDefaultNiagaraSystem(VFXType);
    }
    
    if (!NiagaraSystem)
    {
        UE_LOG(LogTemp, Warning, TEXT("SigilVFXManager: No Niagara system available for VFX type %d"), (int32)VFXType);
        return Instance;
    }
    
    // Obter componente do pool ou criar novo
    UNiagaraComponent* NiagaraComponent = nullptr;
    if (bUsePooling)
    {
        NiagaraComponent = GetPooledComponent(NiagaraSystem);
    }
    
    if (!NiagaraComponent)
    {
        // Criar novo componente
        NiagaraComponent = NewObject<UNiagaraComponent>(TargetActor);
        if (NiagaraComponent)
        {
            NiagaraComponent->SetAsset(NiagaraSystem);
        }
    }
    
    if (!NiagaraComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("SigilVFXManager: Failed to create Niagara component"));
        return Instance;
    }
    
    // Configurar componente
    ConfigureNiagaraComponent(NiagaraComponent, Config, TargetActor);
    
    // Ativar componente
    NiagaraComponent->Activate(true);
    
    Instance.NiagaraComponent = NiagaraComponent;
    Instance.bIsActive = true;
    
    return Instance;
}

UNiagaraComponent* USigilVFXManager::GetPooledComponent(UNiagaraSystem* NiagaraSystem)
{
    if (!NiagaraSystem)
    {
        return nullptr;
    }
    
    TSoftObjectPtr<UNiagaraSystem> SystemPtr(NiagaraSystem);
    
    if (!ComponentPools.Contains(SystemPtr))
    {
        CreatePool(NiagaraSystem, DEFAULT_POOL_SIZE);
    }
    
    FSigilVFXPool& Pool = ComponentPools[SystemPtr];
    
    if (Pool.AvailableComponents.Num() > 0)
    {
        UNiagaraComponent* Component = Pool.AvailableComponents.Pop();
        Pool.ActiveComponents.Add(Component);
        return Component;
    }
    
    // Pool vazio, expandir se possível
    if (Pool.ActiveComponents.Num() + Pool.AvailableComponents.Num() < Pool.MaxPoolSize)
    {
        ExpandPool(NiagaraSystem, 1);
        if (Pool.AvailableComponents.Num() > 0)
        {
            UNiagaraComponent* Component = Pool.AvailableComponents.Pop();
            Pool.ActiveComponents.Add(Component);
            return Component;
        }
    }
    
    return nullptr;
}

void USigilVFXManager::ReturnComponentToPool(UNiagaraComponent* Component, UNiagaraSystem* NiagaraSystem)
{
    if (!Component || !NiagaraSystem)
    {
        return;
    }
    
    TSoftObjectPtr<UNiagaraSystem> SystemPtr(NiagaraSystem);
    
    if (!ComponentPools.Contains(SystemPtr))
    {
        Component->DestroyComponent();
        return;
    }
    
    FSigilVFXPool& Pool = ComponentPools[SystemPtr];
    
    // Remover da lista de ativos
    Pool.ActiveComponents.Remove(Component);
    
    // Resetar componente
    Component->Deactivate();
    Component->DetachFromComponent(FDetachmentTransformRules::KeepWorldTransform);
    
    // Adicionar de volta ao pool
    Pool.AvailableComponents.Add(Component);
}

void USigilVFXManager::CreatePool(UNiagaraSystem* NiagaraSystem, int32 InitialSize)
{
    if (!NiagaraSystem || InitialSize <= 0)
    {
        return;
    }
    
    TSoftObjectPtr<UNiagaraSystem> SystemPtr(NiagaraSystem);
    
    FSigilVFXPool NewPool;
    NewPool.InitialPoolSize = InitialSize;
    NewPool.MaxPoolSize = FMath::Max(InitialSize * 5, MAX_POOL_SIZE);
    NewPool.AssociatedSystem = SystemPtr;
    
    // Criar componentes iniciais
    for (int32 i = 0; i < InitialSize; ++i)
    {
        UNiagaraComponent* Component = NewObject<UNiagaraComponent>(GetOwner());
        if (Component)
        {
            Component->SetAsset(NiagaraSystem);
            Component->SetAutoActivate(false);
            NewPool.AvailableComponents.Add(Component);
        }
    }
    
    ComponentPools.Add(SystemPtr, NewPool);
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Created pool for %s with %d components"), 
        *NiagaraSystem->GetName(), InitialSize);
}

void USigilVFXManager::ExpandPool(UNiagaraSystem* NiagaraSystem, int32 AdditionalSize)
{
    if (!NiagaraSystem || AdditionalSize <= 0)
    {
        return;
    }
    
    TSoftObjectPtr<UNiagaraSystem> SystemPtr(NiagaraSystem);
    
    if (!ComponentPools.Contains(SystemPtr))
    {
        CreatePool(NiagaraSystem, AdditionalSize);
        return;
    }
    
    FSigilVFXPool& Pool = ComponentPools[SystemPtr];
    
    int32 CurrentSize = Pool.AvailableComponents.Num() + Pool.ActiveComponents.Num();
    int32 NewSize = FMath::Min(CurrentSize + AdditionalSize, Pool.MaxPoolSize);
    int32 ComponentsToCreate = NewSize - CurrentSize;
    
    for (int32 i = 0; i < ComponentsToCreate; ++i)
    {
        UNiagaraComponent* Component = NewObject<UNiagaraComponent>(GetOwner());
        if (Component)
        {
            Component->SetAsset(NiagaraSystem);
            Component->SetAutoActivate(false);
            Pool.AvailableComponents.Add(Component);
        }
    }
    
    UE_LOG(LogTemp, Verbose, TEXT("SigilVFXManager: Expanded pool for %s by %d components"), 
        *NiagaraSystem->GetName(), ComponentsToCreate);
}

void USigilVFXManager::ConfigureNiagaraComponent(UNiagaraComponent* Component, const FSigilVFXConfig& Config, AActor* TargetActor)
{
    if (!Component || !TargetActor)
    {
        return;
    }
    
    // Anexar ao ator se necessário
    if (Config.bAttachToActor)
    {
        USceneComponent* AttachComponent = TargetActor->GetRootComponent();
        
        // Tentar anexar a um socket específico se especificado
        if (Config.AttachSocketName != NAME_None)
        {
            USkeletalMeshComponent* SkeletalMesh = TargetActor->FindComponentByClass<USkeletalMeshComponent>();
            if (SkeletalMesh && SkeletalMesh->DoesSocketExist(Config.AttachSocketName))
            {
                AttachComponent = SkeletalMesh;
            }
        }
        
        if (AttachComponent)
        {
            Component->AttachToComponent(AttachComponent, 
                FAttachmentTransformRules::SnapToTargetIncludingScale, 
                Config.AttachSocketName);
        }
    }
    
    // Configurar transformação
    Component->SetRelativeLocation(Config.RelativeOffset);
    Component->SetRelativeScale3D(Config.Scale);
    
    // Configurar loop
    if (Config.bShouldLoop)
    {
        Component->SetAutoActivate(true);
    }
    
    // Aplicar parâmetros customizados
    ApplyCustomParameters(Component, Config);
}

void USigilVFXManager::ApplyCustomParameters(UNiagaraComponent* Component, const FSigilVFXConfig& Config)
{
    if (!Component)
    {
        return;
    }
    
    // Aplicar parâmetros float
    for (const auto& Pair : Config.FloatParameters)
    {
        Component->SetFloatParameter(FName(*Pair.Key), Pair.Value);
    }
    
    // Aplicar parâmetros vector
    for (const auto& Pair : Config.VectorParameters)
    {
        Component->SetVectorParameter(FName(*Pair.Key), Pair.Value);
    }
    
    // Aplicar parâmetros color
    for (const auto& Pair : Config.ColorParameters)
    {
        Component->SetColorParameter(FName(*Pair.Key), Pair.Value);
    }
    
    // Aplicar cor principal
    Component->SetColorParameter("Color", Config.Color);
}

void USigilVFXManager::UpdateActiveInstances(float DeltaTime)
{
    TArray<int32> InstancesToRemove;
    
    for (auto& Pair : ActiveVFXInstances)
    {
        FSigilVFXInstance& Instance = Pair.Value;
        
        // Verificar se o ator ainda é válido
        if (!Instance.OwnerActor.IsValid())
        {
            InstancesToRemove.Add(Pair.Key);
            continue;
        }
        
        // Verificar se o componente ainda é válido
        if (!IsValid(Instance.NiagaraComponent))
        {
            InstancesToRemove.Add(Pair.Key);
            continue;
        }
        
        // Atualizar tempo restante (apenas para efeitos com duração limitada)
        if (Instance.Config.Duration > 0.0f)
        {
            Instance.RemainingTime -= DeltaTime;
            
            if (Instance.RemainingTime <= 0.0f)
            {
                InstancesToRemove.Add(Pair.Key);
                continue;
            }
        }
        
        // Verificar se o efeito ainda está ativo
        if (!Instance.NiagaraComponent->IsActive())
        {
            // Se não deveria ter parado, reativar
            if (Instance.Config.bShouldLoop && Instance.RemainingTime > 0.0f)
            {
                Instance.NiagaraComponent->Activate(true);
            }
            else
            {
                InstancesToRemove.Add(Pair.Key);
            }
        }
    }
    
    // Remover instâncias expiradas
    for (int32 InstanceID : InstancesToRemove)
    {
        RemoveExpiredInstance(InstanceID);
    }
}

void USigilVFXManager::RemoveExpiredInstance(int32 InstanceID)
{
    if (!ActiveVFXInstances.Contains(InstanceID))
    {
        return;
    }
    
    FSigilVFXInstance& Instance = ActiveVFXInstances[InstanceID];
    
    // Parar o componente
    if (Instance.NiagaraComponent && IsValid(Instance.NiagaraComponent))
    {
        Instance.NiagaraComponent->Deactivate();
        
        // Retornar ao pool se estiver usando pooling
        if (bUsePooling && Instance.Config.NiagaraSystem.IsValid())
        {
            ReturnComponentToPool(Instance.NiagaraComponent, Instance.Config.NiagaraSystem.Get());
        }
        else
        {
            Instance.NiagaraComponent->DestroyComponent();
        }
    }
    
    // Disparar evento
    OnVFXCompleted.Broadcast(Instance.VFXType, InstanceID);
    
    // Remover da lista
    ActiveVFXInstances.Remove(InstanceID);
}

void USigilVFXManager::PerformAutomaticCleanup()
{
    // Limpar pools não utilizados
    CleanupUnusedPools();
    
    // Verificar se há muitos efeitos ativos
    if (ActiveVFXInstances.Num() > MaxSimultaneousEffects)
    {
        int32 EffectsToRemove = ActiveVFXInstances.Num() - MaxSimultaneousEffects;
        RemoveLowPriorityEffects(EffectsToRemove);
    }
    
    // Verificar efeitos muito antigos
    float CurrentTime = GetWorld()->GetTimeSeconds();
    TArray<int32> OldEffects;
    
    for (const auto& Pair : ActiveVFXInstances)
    {
        const FSigilVFXInstance& Instance = Pair.Value;
        if (CurrentTime - Instance.CreationTime > MAX_EFFECT_LIFETIME)
        {
            OldEffects.Add(Pair.Key);
        }
    }
    
    for (int32 InstanceID : OldEffects)
    {
        StopVFXEffect(InstanceID);
    }
    
    UE_LOG(LogTemp, Verbose, TEXT("SigilVFXManager: Automatic cleanup completed, removed %d old effects"), OldEffects.Num());
}

void USigilVFXManager::UpdateStats()
{
    CurrentStats.ActiveEffects = ActiveVFXInstances.Num();
    CurrentStats.PooledComponents = 0;
    CurrentStats.EffectsByType.Empty();
    CurrentStats.EffectsByPriority.Empty();
    
    // Contar componentes no pool
    for (const auto& Pair : ComponentPools)
    {
        const FSigilVFXPool& Pool = Pair.Value;
        CurrentStats.PooledComponents += Pool.AvailableComponents.Num();
    }
    
    // Contar efeitos por tipo e prioridade
    float TotalLifetime = 0.0f;
    float CurrentTime = GetWorld()->GetTimeSeconds();
    
    for (const auto& Pair : ActiveVFXInstances)
    {
        const FSigilVFXInstance& Instance = Pair.Value;
        
        // Por tipo
        if (CurrentStats.EffectsByType.Contains(Instance.VFXType))
        {
            CurrentStats.EffectsByType[Instance.VFXType]++;
        }
        else
        {
            CurrentStats.EffectsByType.Add(Instance.VFXType, 1);
        }
        
        // Por prioridade
        if (CurrentStats.EffectsByPriority.Contains(Instance.Config.Priority))
        {
            CurrentStats.EffectsByPriority[Instance.Config.Priority]++;
        }
        else
        {
            CurrentStats.EffectsByPriority.Add(Instance.Config.Priority, 1);
        }
        
        // Tempo de vida
        TotalLifetime += CurrentTime - Instance.CreationTime;
    }
    
    // Calcular tempo médio de vida
    if (ActiveVFXInstances.Num() > 0)
    {
        CurrentStats.AverageEffectLifetime = TotalLifetime / ActiveVFXInstances.Num();
    }
    
    // Atualizar pico
    if (CurrentStats.ActiveEffects > CurrentStats.PeakSimultaneousEffects)
    {
        CurrentStats.PeakSimultaneousEffects = CurrentStats.ActiveEffects;
    }
    
    // Calcular eficiência do pool
    int32 TotalPoolComponents = CurrentStats.PooledComponents;
    int32 ActivePoolComponents = 0;
    
    for (const auto& Pair : ComponentPools)
    {
        const FSigilVFXPool& Pool = Pair.Value;
        ActivePoolComponents += Pool.ActiveComponents.Num();
        TotalPoolComponents += Pool.ActiveComponents.Num();
    }
    
    if (TotalPoolComponents > 0)
    {
        CurrentStats.PoolEfficiency = (float)ActivePoolComponents / TotalPoolComponents * 100.0f;
    }
    
    // Disparar evento de mudança de stats
    OnVFXStatsChanged.Broadcast(CurrentStats);
}

bool USigilVFXManager::ValidateVFXConfig(const FSigilVFXConfig& Config) const
{
    // Verificar duração
    if (Config.Duration < 0.0f)
    {
        return false;
    }
    
    // Verificar escala
    if (Config.Scale.IsNearlyZero())
    {
        return false;
    }
    
    // Verificar cor
    if (Config.Color.A <= 0.0f)
    {
        return false;
    }
    
    return true;
}

FSigilVFXConfig USigilVFXManager::GetSigilBasedConfig(ASigilItem* Sigil, ESigilVFXType VFXType) const
{
    FSigilVFXConfig Config = GetDefaultVFXConfig(VFXType);
    
    if (!Sigil)
    {
        return Config;
    }
    
    const FSigilData& SigilData = Sigil->SigilData;
    
    // Aplicar modificações baseadas na raridade
    if (RarityVFXConfigs.Contains(SigilData.Rarity))
    {
        const FSigilVFXConfig& RarityConfig = RarityVFXConfigs[SigilData.Rarity];
        Config.Scale = RarityConfig.Scale;
        Config.Color = FLinearColor::LerpUsingHSV(Config.Color, RarityConfig.Color, 0.7f);
    }
    
    // Aplicar VFX específico do sigilo se disponível
    switch (VFXType)
    {
        case ESigilVFXType::Equip:
            if (SigilData.EquipVFX.IsValid())
            {
                Config.NiagaraSystem = SigilData.EquipVFX;
            }
            break;
        case ESigilVFXType::FusionStart:
        case ESigilVFXType::FusionProgress:
        case ESigilVFXType::FusionComplete:
            if (SigilData.FusionVFX.IsValid())
            {
                Config.NiagaraSystem = SigilData.FusionVFX;
            }
            break;
        case ESigilVFXType::SpectralAura:
        case ESigilVFXType::SpectralPower:
            if (SigilData.AuraVFX.IsValid())
            {
                Config.NiagaraSystem = SigilData.AuraVFX;
            }
            break;
        default:
            // Para outros tipos, usar o sistema padrão
            break;
    }
    
    return Config;
}

bool USigilVFXManager::CanCreateNewEffect(AActor* TargetActor) const
{
    // Verificar limite global
    if (ActiveVFXInstances.Num() >= MaxSimultaneousEffects)
    {
        return false;
    }
    
    // Verificar limite por ator
    if (TargetActor)
    {
        int32 EffectsForActor = 0;
        for (const auto& Pair : ActiveVFXInstances)
        {
            if (Pair.Value.OwnerActor.Get() == TargetActor)
            {
                EffectsForActor++;
            }
        }
        
        if (EffectsForActor >= MaxEffectsPerActor)
        {
            return false;
        }
    }
    
    return true;
}

void USigilVFXManager::RemoveLowPriorityEffects(int32 CountToRemove)
{
    if (CountToRemove <= 0)
    {
        return;
    }
    
    // Criar lista de efeitos ordenada por prioridade (menor primeiro)
    TArray<TPair<int32, ESigilVFXPriority>> EffectPriorities;
    
    for (const auto& Pair : ActiveVFXInstances)
    {
        EffectPriorities.Add(TPair<int32, ESigilVFXPriority>(Pair.Key, Pair.Value.Config.Priority));
    }
    
    // Ordenar por prioridade
    EffectPriorities.Sort([](const TPair<int32, ESigilVFXPriority>& A, const TPair<int32, ESigilVFXPriority>& B) {
        return A.Value < B.Value;
    });
    
    // Remover os efeitos de menor prioridade
    int32 RemovedCount = 0;
    for (const auto& Pair : EffectPriorities)
    {
        if (RemovedCount >= CountToRemove)
        {
            break;
        }
        
        // Não remover efeitos críticos
        if (Pair.Value != ESigilVFXPriority::Critical)
        {
            StopVFXEffect(Pair.Key);
            RemovedCount++;
        }
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Removed %d low priority effects"), RemovedCount);
}

// ========================================
// DEBUG FUNCTIONS
// ========================================

void USigilVFXManager::PrintVFXStats() const
{
    UE_LOG(LogTemp, Log, TEXT("=== SIGIL VFX STATS ==="));
    UE_LOG(LogTemp, Log, TEXT("Active Effects: %d"), CurrentStats.ActiveEffects);
    UE_LOG(LogTemp, Log, TEXT("Pooled Components: %d"), CurrentStats.PooledComponents);
    UE_LOG(LogTemp, Log, TEXT("Peak Simultaneous Effects: %d"), CurrentStats.PeakSimultaneousEffects);
    UE_LOG(LogTemp, Log, TEXT("Average Effect Lifetime: %.2f seconds"), CurrentStats.AverageEffectLifetime);
    UE_LOG(LogTemp, Log, TEXT("Pool Efficiency: %.1f%%"), CurrentStats.PoolEfficiency);
    
    UE_LOG(LogTemp, Log, TEXT("Effects by Type:"));
    for (const auto& Pair : CurrentStats.EffectsByType)
    {
        UE_LOG(LogTemp, Log, TEXT("  %d: %d effects"), (int32)Pair.Key, Pair.Value);
    }
    
    UE_LOG(LogTemp, Log, TEXT("Effects by Priority:"));
    for (const auto& Pair : CurrentStats.EffectsByPriority)
    {
        UE_LOG(LogTemp, Log, TEXT("  %d: %d effects"), (int32)Pair.Key, Pair.Value);
    }
}

void USigilVFXManager::PrintPoolInfo() const
{
    UE_LOG(LogTemp, Log, TEXT("=== VFX POOL INFO ==="));
    UE_LOG(LogTemp, Log, TEXT("Total Pools: %d"), ComponentPools.Num());
    
    for (const auto& Pair : ComponentPools)
    {
        const FSigilVFXPool& Pool = Pair.Value;
        UNiagaraSystem* System = Pair.Key.Get();
        FString SystemName = System ? System->GetName() : TEXT("Unknown");
        
        UE_LOG(LogTemp, Log, TEXT("Pool %s:"), *SystemName);
        UE_LOG(LogTemp, Log, TEXT("  Available: %d"), Pool.AvailableComponents.Num());
        UE_LOG(LogTemp, Log, TEXT("  Active: %d"), Pool.ActiveComponents.Num());
        UE_LOG(LogTemp, Log, TEXT("  Max Size: %d"), Pool.MaxPoolSize);
        UE_LOG(LogTemp, Log, TEXT("  Initial Size: %d"), Pool.InitialPoolSize);
    }
}

void USigilVFXManager::ForceCleanupAllPools()
{
    for (auto& Pair : ComponentPools)
    {
        FSigilVFXPool& Pool = Pair.Value;
        
        // Cleanup all components in pool
        for (UNiagaraComponent* Component : Pool.AvailableComponents)
        {
            if (IsValid(Component))
            {
                Component->DestroyComponent();
            }
        }
        Pool.AvailableComponents.Empty();
        
        for (UNiagaraComponent* Component : Pool.ActiveComponents)
        {
            if (IsValid(Component))
            {
                Component->DestroyComponent();
            }
        }
        Pool.ActiveComponents.Empty();
    }
    
    ComponentPools.Empty();
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: Force cleanup completed for all pools"));
}

void USigilVFXManager::ResetVFXStats()
{
    // Resetar todas as estatísticas para valores iniciais
    CurrentStats.ActiveEffects = 0;
    CurrentStats.PooledComponents = 0;
    CurrentStats.AverageEffectLifetime = 0.0f;
    CurrentStats.PeakSimultaneousEffects = 0;
    CurrentStats.PoolEfficiency = 0.0f;
    CurrentStats.EffectsByType.Empty();
    CurrentStats.EffectsByPriority.Empty();
    
    // Recalcular componentes pooled atuais
    for (const auto& Pair : ComponentPools)
    {
        const FSigilVFXPool& Pool = Pair.Value;
        CurrentStats.PooledComponents += Pool.AvailableComponents.Num();
    }
    
    // Recalcular efeitos ativos atuais
    CurrentStats.ActiveEffects = ActiveVFXInstances.Num();
    
    // Recalcular estatísticas por tipo e prioridade
    for (const auto& Pair : ActiveVFXInstances)
    {
        const FSigilVFXInstance& Instance = Pair.Value;
        CurrentStats.EffectsByType.FindOrAdd(Instance.VFXType)++;
        CurrentStats.EffectsByPriority.FindOrAdd(Instance.Config.Priority)++;
    }
    
    // Disparar evento de mudança de estatísticas
    OnVFXStatsChanged.Broadcast(CurrentStats);
    
    UE_LOG(LogTemp, Log, TEXT("SigilVFXManager: VFX stats have been reset"));
}

int32 USigilVFXManager::GetOptimalPoolSize(ESigilVFXType VFXType) const
{
    // Determinar tamanho do pool baseado no tipo de VFX e frequência de uso
    switch (VFXType)
    {
        case ESigilVFXType::Equip:
        case ESigilVFXType::Unequip:
            // Efeitos de equipar/desequipar são muito frequentes
            return 20;
            
        case ESigilVFXType::FusionStart:
        case ESigilVFXType::FusionProgress:
        case ESigilVFXType::FusionComplete:
            // Efeitos de fusão são moderadamente frequentes
            return 15;
            
        case ESigilVFXType::SpectralAura:
        case ESigilVFXType::SpectralPower:
        case ESigilVFXType::TeamFight:
            // Efeitos de aura e combate em equipe são frequentes
            return 12;
            
        case ESigilVFXType::Critical:
            // Efeitos críticos são muito frequentes em combate
            return 25;
            
        case ESigilVFXType::Reforge:
        case ESigilVFXType::LevelUp:
            // Efeitos de reforge e level up são menos frequentes
            return 8;
            
        case ESigilVFXType::Death:
        case ESigilVFXType::Respawn:
            // Efeitos de morte e respawn são moderados
            return 10;
            
        case ESigilVFXType::Objective:
            // Efeitos de objetivo são raros
            return 5;
            
        case ESigilVFXType::None:
        default:
            // Tamanho padrão para tipos não especificados
            return DEFAULT_POOL_SIZE;
    }
}

int32 USigilVFXManager::GetRarityPoolSize(ESigilRarity Rarity) const
{
    // Determinar tamanho do pool baseado na raridade
    // Raridades mais altas são menos frequentes, então pools menores
    switch (Rarity)
    {
        case ESigilRarity::Common:
            // Sigilos comuns são muito frequentes
            return 15;
            
        case ESigilRarity::Rare:
            // Sigilos raros são menos frequentes
            return 8;
            
        case ESigilRarity::Epic:
            // Sigilos épicos são raros
            return 5;
            
        case ESigilRarity::Legendary:
            // Sigilos lendários são muito raros
            return 3;
            
        default:
            // Tamanho padrão para raridades não especificadas
            return DEFAULT_POOL_SIZE / 2;
    }
}

// ========================================
// IMPLEMENTAÇÕES DAS FUNÇÕES FALTANTES
// ========================================

UNiagaraSystem* USigilVFXManager::GetDefaultNiagaraSystem(ESigilVFXType VFXType) const
{
    // Mapeamento de tipos VFX para sistemas Niagara padrão
    static TMap<ESigilVFXType, TSoftObjectPtr<UNiagaraSystem>> DefaultSystems;

    // Inicializar mapeamento na primeira chamada
    if (DefaultSystems.Num() == 0)
    {
        DefaultSystems.Add(ESigilVFXType::Equip, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_SigilEquip")));
        DefaultSystems.Add(ESigilVFXType::Unequip, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_SigilUnequip")));
        DefaultSystems.Add(ESigilVFXType::FusionStart, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_FusionStart")));
        DefaultSystems.Add(ESigilVFXType::FusionProgress, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_FusionProgress")));
        DefaultSystems.Add(ESigilVFXType::FusionComplete, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_FusionComplete")));
        DefaultSystems.Add(ESigilVFXType::Reforge, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_SigilReforge")));
        DefaultSystems.Add(ESigilVFXType::LevelUp, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_SigilLevelUp")));
        DefaultSystems.Add(ESigilVFXType::SpectralPower, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_SpectralPower")));
        DefaultSystems.Add(ESigilVFXType::SpectralAura, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_SpectralAura")));
        DefaultSystems.Add(ESigilVFXType::TeamFight, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_TeamFight")));
        DefaultSystems.Add(ESigilVFXType::Objective, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_Objective")));
        DefaultSystems.Add(ESigilVFXType::Critical, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_Critical")));
        DefaultSystems.Add(ESigilVFXType::Death, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_Death")));
        DefaultSystems.Add(ESigilVFXType::Respawn, TSoftObjectPtr<UNiagaraSystem>(TEXT("/Game/VFX/Sigils/NS_Respawn")));
    }

    // Buscar sistema para o tipo especificado
    if (DefaultSystems.Contains(VFXType))
    {
        TSoftObjectPtr<UNiagaraSystem> SystemPtr = DefaultSystems[VFXType];
        if (SystemPtr.IsValid())
        {
            return SystemPtr.LoadSynchronous();
        }
        else
        {
            // Tentar carregar assincronamente se não estiver carregado
            UAssetManager& AssetManager = UAssetManager::Get();
            FStreamableManager& StreamableManager = AssetManager.GetStreamableManager();

            TSharedPtr<FStreamableHandle> Handle = StreamableManager.RequestAsyncLoad(SystemPtr.ToSoftObjectPath());
            if (Handle.IsValid() && Handle->HasLoadCompleted())
            {
                return SystemPtr.Get();
            }
        }
    }

    // Fallback: tentar carregar sistema genérico
    static TSoftObjectPtr<UNiagaraSystem> GenericSystem(TEXT("/Game/VFX/Sigils/NS_Generic"));
    if (GenericSystem.IsValid())
    {
        return GenericSystem.LoadSynchronous();
    }

    UE_LOGFMT(LogTemp, Warning, "Failed to load default Niagara system for VFX type {0}", (int32)VFXType);
    return nullptr;
}

int32 USigilVFXManager::PlayVFXAtLocation(ESigilVFXType VFXType, const FVector& Location, const FSigilVFXConfig& Config)
{
    if (!bIsInitialized || VFXType == ESigilVFXType::None)
    {
        return -1;
    }

    // Verificar se pode criar novo efeito
    if (ActiveVFXInstances.Num() >= MaxSimultaneousEffects)
    {
        // Remover efeitos de baixa prioridade se necessário
        RemoveLowPriorityEffects(1);

        if (ActiveVFXInstances.Num() >= MaxSimultaneousEffects)
        {
            UE_LOGFMT(LogTemp, Warning, "Cannot create new VFX at location, limits reached");
            return -1;
        }
    }

    // Validar configuração
    if (!ValidateVFXConfig(Config))
    {
        UE_LOGFMT(LogTemp, Warning, "Invalid VFX config for location spawn");
        return -1;
    }

    // Criar ator temporário na localização para anexar o VFX
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOGFMT(LogTemp, Error, "Invalid world for VFX location spawn");
        return -1;
    }

    // Criar nova instância VFX
    FSigilVFXInstance NewInstance = CreateVFXInstanceAtLocation(VFXType, Location, Config);
    if (!NewInstance.bIsActive)
    {
        return -1;
    }

    // Adicionar à lista de instâncias ativas
    ActiveVFXInstances.Add(NewInstance.InstanceID, NewInstance);

    // Disparar evento
    OnVFXStarted.Broadcast(VFXType, NewInstance.InstanceID);

    UE_LOGFMT(LogTemp, Verbose, "Started VFX {0} of type {1} at location {2}",
              NewInstance.InstanceID, (int32)VFXType, Location.ToString());

    return NewInstance.InstanceID;
}

FSigilVFXInstance USigilVFXManager::CreateVFXInstanceAtLocation(ESigilVFXType VFXType, const FVector& Location, const FSigilVFXConfig& Config)
{
    FSigilVFXInstance Instance;
    Instance.VFXType = VFXType;
    Instance.Config = Config;
    Instance.OwnerActor = nullptr; // Sem ator owner para VFX de localização
    Instance.InstanceID = NextInstanceID++;
    Instance.CreationTime = GetWorld()->GetTimeSeconds();
    Instance.RemainingTime = Config.Duration;

    // Obter sistema Niagara
    UNiagaraSystem* NiagaraSystem = Config.NiagaraSystem.Get();
    if (!NiagaraSystem)
    {
        // Usar sistema padrão se não especificado
        NiagaraSystem = GetDefaultNiagaraSystem(VFXType);
    }

    if (!NiagaraSystem)
    {
        UE_LOGFMT(LogTemp, Warning, "No Niagara system available for VFX type {0} at location", (int32)VFXType);
        return Instance;
    }

    // Usar Niagara Function Library para spawn na localização
    UNiagaraComponent* NiagaraComponent = UNiagaraFunctionLibrary::SpawnSystemAtLocation(
        GetWorld(),
        NiagaraSystem,
        Location,
        FRotator::ZeroRotator,
        Config.Scale,
        true, // Auto destroy
        true, // Auto activate
        ENCPoolMethod::None, // Não usar pooling interno do Niagara
        true // Pre cull check
    );

    if (!NiagaraComponent)
    {
        UE_LOGFMT(LogTemp, Error, "Failed to spawn Niagara component at location {0}", Location.ToString());
        return Instance;
    }

    // Configurar componente
    ConfigureNiagaraComponentAtLocation(NiagaraComponent, Config, Location);

    Instance.NiagaraComponent = NiagaraComponent;
    Instance.bIsActive = true;

    return Instance;
}

void USigilVFXManager::ConfigureNiagaraComponentAtLocation(UNiagaraComponent* Component, const FSigilVFXConfig& Config, const FVector& Location)
{
    if (!Component)
    {
        return;
    }

    // Configurar transformação
    Component->SetWorldLocation(Location + Config.RelativeOffset);
    Component->SetWorldScale3D(Config.Scale);

    // Configurar loop
    if (Config.bShouldLoop && Config.Duration > 0.0f)
    {
        // Para efeitos com loop e duração limitada, configurar timer para parar
        FTimerHandle TimerHandle;
        GetWorld()->GetTimerManager().SetTimer(TimerHandle, [Component]()
        {
            if (IsValid(Component))
            {
                Component->Deactivate();
            }
        }, Config.Duration, false);
    }

    // Aplicar parâmetros customizados
    ApplyCustomParameters(Component, Config);

    // Configurar auto-destruição se não for loop infinito
    if (Config.Duration > 0.0f)
    {
        Component->SetAutoDestroy(true);
    }
}

// ========================================
// IMPLEMENTAÇÕES ROBUSTAS DE CARREGAMENTO
// ========================================

void USigilVFXManager::LoadDefaultVFXSystems()
{
    // Carregar sistemas VFX padrão de forma assíncrona para melhor performance
    TArray<FSoftObjectPath> SystemsToLoad;

    // Adicionar todos os sistemas padrão à lista de carregamento
    SystemsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Sigils/NS_SigilEquip")));
    SystemsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Sigils/NS_SigilUnequip")));
    SystemsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Sigils/NS_FusionStart")));
    SystemsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Sigils/NS_FusionProgress")));
    SystemsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Sigils/NS_FusionComplete")));
    SystemsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Sigils/NS_SigilReforge")));
    SystemsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Sigils/NS_SpectralAura")));
    SystemsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Sigils/NS_SpectralPower")));
    SystemsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Sigils/NS_Critical")));
    SystemsToLoad.Add(FSoftObjectPath(TEXT("/Game/VFX/Sigils/NS_TeamFight")));

    // Carregar assincronamente
    UAssetManager& AssetManager = UAssetManager::Get();
    FStreamableManager& StreamableManager = AssetManager.GetStreamableManager();

    TSharedPtr<FStreamableHandle> Handle = StreamableManager.RequestAsyncLoad(
        SystemsToLoad,
        FStreamableDelegate::CreateUObject(this, &USigilVFXManager::OnDefaultVFXSystemsLoaded),
        FStreamableManager::AsyncLoadHighPriority
    );

    if (Handle.IsValid())
    {
        UE_LOGFMT(LogTemp, Log, "Started async loading of {0} default VFX systems", SystemsToLoad.Num());
    }
    else
    {
        UE_LOGFMT(LogTemp, Warning, "Failed to start async loading of default VFX systems");
    }
}

void USigilVFXManager::OnDefaultVFXSystemsLoaded()
{
    UE_LOGFMT(LogTemp, Log, "Default VFX systems loaded successfully");

    // Atualizar configurações padrão com sistemas carregados
    UpdateDefaultConfigsWithLoadedSystems();

    // Pré-carregar pools com sistemas carregados
    if (bUsePooling)
    {
        PreloadCommonVFXPools();
    }
}

void USigilVFXManager::UpdateDefaultConfigsWithLoadedSystems()
{
    // Atualizar configurações padrão com sistemas Niagara carregados
    struct FVFXSystemMapping
    {
        ESigilVFXType VFXType;
        FString SystemPath;
    };

    TArray<FVFXSystemMapping> SystemMappings = {
        {ESigilVFXType::Equip, TEXT("/Game/VFX/Sigils/NS_SigilEquip")},
        {ESigilVFXType::Unequip, TEXT("/Game/VFX/Sigils/NS_SigilUnequip")},
        {ESigilVFXType::FusionStart, TEXT("/Game/VFX/Sigils/NS_FusionStart")},
        {ESigilVFXType::FusionProgress, TEXT("/Game/VFX/Sigils/NS_FusionProgress")},
        {ESigilVFXType::FusionComplete, TEXT("/Game/VFX/Sigils/NS_FusionComplete")},
        {ESigilVFXType::Reforge, TEXT("/Game/VFX/Sigils/NS_SigilReforge")},
        {ESigilVFXType::LevelUp, TEXT("/Game/VFX/Sigils/NS_SigilLevelUp")},
        {ESigilVFXType::SpectralPower, TEXT("/Game/VFX/Sigils/NS_SpectralPower")},
        {ESigilVFXType::SpectralAura, TEXT("/Game/VFX/Sigils/NS_SpectralAura")},
        {ESigilVFXType::TeamFight, TEXT("/Game/VFX/Sigils/NS_TeamFight")},
        {ESigilVFXType::Objective, TEXT("/Game/VFX/Sigils/NS_Objective")},
        {ESigilVFXType::Critical, TEXT("/Game/VFX/Sigils/NS_Critical")},
        {ESigilVFXType::Death, TEXT("/Game/VFX/Sigils/NS_Death")},
        {ESigilVFXType::Respawn, TEXT("/Game/VFX/Sigils/NS_Respawn")}
    };

    int32 SystemsUpdated = 0;

    for (const FVFXSystemMapping& Mapping : SystemMappings)
    {
        if (DefaultVFXConfigs.Contains(Mapping.VFXType))
        {
            FSigilVFXConfig& Config = DefaultVFXConfigs[Mapping.VFXType];

            // Tentar carregar o sistema
            TSoftObjectPtr<UNiagaraSystem> SystemPtr(Mapping.SystemPath);
            if (UNiagaraSystem* LoadedSystem = SystemPtr.LoadSynchronous())
            {
                Config.NiagaraSystem = SystemPtr;
                SystemsUpdated++;

                UE_LOGFMT(LogTemp, Verbose, "Updated default config for VFX type {0} with system {1}",
                         (int32)Mapping.VFXType, Mapping.SystemPath);
            }
            else
            {
                UE_LOGFMT(LogTemp, Warning, "Failed to load system {0} for VFX type {1}",
                         Mapping.SystemPath, (int32)Mapping.VFXType);
            }
        }
    }

    UE_LOGFMT(LogTemp, Log, "Updated {0} default VFX configs with loaded systems", SystemsUpdated);
}

// ========================================
// IMPLEMENTAÇÕES ROBUSTAS DE VALIDAÇÃO
// ========================================

bool USigilVFXManager::ValidateVFXSystem(UNiagaraSystem* System) const
{
    if (!System)
    {
        return false;
    }

    // Verificar se o sistema está válido e compilado
    if (!System->IsValidLowLevel() || System->IsPendingKill())
    {
        return false;
    }

    // Verificar se o sistema tem emitters
    if (System->GetSystemSpawnScript() == nullptr)
    {
        UE_LOGFMT(LogTemp, Warning, "Niagara system {0} has no spawn script", System->GetName());
        return false;
    }

    // Verificar se o sistema não está sendo compilado
    if (System->HasOutstandingCompilationRequests())
    {
        UE_LOGFMT(LogTemp, Warning, "Niagara system {0} has outstanding compilation requests", System->GetName());
        return false;
    }

    return true;
}

void USigilVFXManager::ValidateAllVFXConfigs()
{
    int32 ValidConfigs = 0;
    int32 InvalidConfigs = 0;

    for (auto& ConfigPair : DefaultVFXConfigs)
    {
        FSigilVFXConfig& Config = ConfigPair.Value;

        if (ValidateVFXConfig(Config))
        {
            // Validar sistema Niagara se especificado
            if (Config.NiagaraSystem.IsValid())
            {
                UNiagaraSystem* System = Config.NiagaraSystem.LoadSynchronous();
                if (ValidateVFXSystem(System))
                {
                    ValidConfigs++;
                }
                else
                {
                    InvalidConfigs++;
                    UE_LOGFMT(LogTemp, Warning, "Invalid Niagara system in config for VFX type {0}",
                             (int32)ConfigPair.Key);
                }
            }
            else
            {
                ValidConfigs++;
            }
        }
        else
        {
            InvalidConfigs++;
            UE_LOGFMT(LogTemp, Warning, "Invalid VFX config for type {0}", (int32)ConfigPair.Key);
        }
    }

    UE_LOGFMT(LogTemp, Log, "VFX Config validation: {0} valid, {1} invalid", ValidConfigs, InvalidConfigs);
}

// ========================================
// IMPLEMENTAÇÕES ROBUSTAS DE PERFORMANCE
// ========================================

void USigilVFXManager::PreloadCommonVFXPools()
{
    if (!bIsInitialized)
    {
        UE_LOGFMT(LogTemp, Warning, "SigilVFXManager: Cannot preload pools - manager not initialized");
        return;
    }

    // Verificar se o pooling está habilitado
    if (!bUsePooling)
    {
        UE_LOGFMT(LogTemp, Log, "SigilVFXManager: Pooling disabled, skipping preload");
        return;
    }

    // Sistemas VFX mais comuns que devem ser pré-carregados
    TArray<ESigilVFXType> CommonVFXTypes = {
        ESigilVFXType::Equip,
        ESigilVFXType::Unequip,
        ESigilVFXType::FusionStart,
        ESigilVFXType::FusionComplete,
        ESigilVFXType::SpectralAura,
        ESigilVFXType::SpectralPower,
        ESigilVFXType::Critical
    };

    int32 TotalPreloadedSystems = 0;
    int32 TotalPreloadedComponents = 0;

    for (ESigilVFXType VFXType : CommonVFXTypes)
    {
        // Obter sistema Niagara para este tipo
        UNiagaraSystem* NiagaraSystem = GetDefaultNiagaraSystem(VFXType);
        if (NiagaraSystem && ValidateVFXSystem(NiagaraSystem))
        {
            // Determinar tamanho do pool baseado no tipo
            int32 PoolSize = GetOptimalPoolSize(VFXType);

            // Criar pool de componentes
            TArray<TObjectPtr<UNiagaraComponent>> ComponentPool;
            ComponentPool.Reserve(PoolSize);

            for (int32 i = 0; i < PoolSize; i++)
            {
                UNiagaraComponent* Component = NewObject<UNiagaraComponent>(this);
                if (Component)
                {
                    Component->SetAsset(NiagaraSystem);
                    Component->SetAutoActivate(false);
                    Component->SetComponentTickEnabled(false);
                    Component->SetVisibility(false);
                    ComponentPool.Add(Component);
                }
            }

            // Armazenar pool
            if (ComponentPool.Num() > 0)
            {
                VFXComponentPools.Add(VFXType, ComponentPool);
                TotalPreloadedSystems++;
                TotalPreloadedComponents += ComponentPool.Num();

                UE_LOGFMT(LogTemp, Log, "SigilVFXManager: Preloaded pool for {0} (Type: {1}) with {2} components",
                    NiagaraSystem->GetName(), (int32)VFXType, PoolSize);
            }
        }
        else
        {
            UE_LOGFMT(LogTemp, Warning, "SigilVFXManager: Failed to load or validate Niagara system for VFX type {0}", (int32)VFXType);
        }
    }

    UE_LOGFMT(LogTemp, Log, "SigilVFXManager: Preload completed - {0} systems, {1} total components",
        TotalPreloadedSystems, TotalPreloadedComponents);
}

int32 USigilVFXManager::GetOptimalPoolSize(ESigilVFXType VFXType) const
{
    // Determinar tamanho ótimo do pool baseado no tipo de VFX
    switch (VFXType)
    {
        case ESigilVFXType::Equip:
        case ESigilVFXType::Unequip:
            return 20; // Efeitos muito comuns

        case ESigilVFXType::FusionStart:
        case ESigilVFXType::FusionComplete:
            return 15; // Efeitos de fusão moderadamente comuns

        case ESigilVFXType::SpectralAura:
        case ESigilVFXType::SpectralPower:
            return 12; // Efeitos espectrais

        case ESigilVFXType::Critical:
            return 25; // Efeitos críticos muito frequentes

        case ESigilVFXType::TeamFight:
        case ESigilVFXType::Objective:
            return 8; // Efeitos situacionais

        case ESigilVFXType::Death:
        case ESigilVFXType::Respawn:
            return 10; // Efeitos de vida/morte

        case ESigilVFXType::Reforge:
        case ESigilVFXType::LevelUp:
            return 5; // Efeitos raros

        default:
            return DEFAULT_POOL_SIZE / 2; // Tamanho padrão para outros tipos
    }
}

void USigilVFXManager::OptimizeVFXPools()
{
    if (!bUsePooling)
    {
        return;
    }

    int32 PoolsOptimized = 0;
    int32 ComponentsRemoved = 0;

    // Otimizar cada pool
    for (auto& PoolPair : VFXComponentPools)
    {
        ESigilVFXType VFXType = PoolPair.Key;
        TArray<TObjectPtr<UNiagaraComponent>>& Pool = PoolPair.Value;

        // Remover componentes inválidos ou não utilizados
        int32 InitialSize = Pool.Num();
        Pool.RemoveAll([](const TObjectPtr<UNiagaraComponent>& Component)
        {
            return !IsValid(Component.Get()) || Component->IsPendingKill();
        });

        int32 RemovedCount = InitialSize - Pool.Num();
        if (RemovedCount > 0)
        {
            ComponentsRemoved += RemovedCount;
            PoolsOptimized++;

            UE_LOGFMT(LogTemp, Log, "Optimized VFX pool for type {0}: removed {1} invalid components",
                     (int32)VFXType, RemovedCount);
        }

        // Redimensionar pool se necessário
        int32 OptimalSize = GetOptimalPoolSize(VFXType);
        if (Pool.Num() > OptimalSize * 1.5f) // Se pool está 50% maior que o ótimo
        {
            int32 ExcessComponents = Pool.Num() - OptimalSize;
            Pool.RemoveAt(OptimalSize, ExcessComponents);
            ComponentsRemoved += ExcessComponents;

            UE_LOGFMT(LogTemp, Log, "Resized VFX pool for type {0}: removed {1} excess components",
                     (int32)VFXType, ExcessComponents);
        }
    }

    if (PoolsOptimized > 0)
    {
        UE_LOGFMT(LogTemp, Log, "VFX pool optimization completed: {0} pools optimized, {1} components removed",
                 PoolsOptimized, ComponentsRemoved);
    }
}

// ========================================
// IMPLEMENTAÇÕES ROBUSTAS DE LIMPEZA
// ========================================

void USigilVFXManager::CleanupExpiredVFX()
{
    if (ActiveVFXInstances.Num() == 0)
    {
        return;
    }

    float CurrentTime = GetWorld()->GetTimeSeconds();
    TArray<int32> ExpiredInstances;

    // Identificar instâncias expiradas
    for (auto& InstancePair : ActiveVFXInstances)
    {
        FSigilVFXInstance& Instance = InstancePair.Value;

        // Verificar se a instância expirou
        bool bShouldRemove = false;

        if (Instance.Config.Duration > 0.0f)
        {
            float ElapsedTime = CurrentTime - Instance.CreationTime;
            if (ElapsedTime >= Instance.Config.Duration)
            {
                bShouldRemove = true;
            }
        }

        // Verificar se o componente Niagara ainda é válido
        if (!bShouldRemove && Instance.NiagaraComponent.IsValid())
        {
            UNiagaraComponent* Component = Instance.NiagaraComponent.Get();
            if (!IsValid(Component) || Component->IsPendingKill() || !Component->IsActive())
            {
                bShouldRemove = true;
            }
        }
        else if (!Instance.NiagaraComponent.IsValid())
        {
            bShouldRemove = true;
        }

        // Verificar se o ator owner ainda é válido (para VFX anexados)
        if (!bShouldRemove && Instance.OwnerActor.IsValid())
        {
            AActor* Owner = Instance.OwnerActor.Get();
            if (!IsValid(Owner))
            {
                bShouldRemove = true;
            }
        }

        if (bShouldRemove)
        {
            ExpiredInstances.Add(Instance.InstanceID);
        }
    }

    // Remover instâncias expiradas
    int32 RemovedCount = 0;
    for (int32 InstanceID : ExpiredInstances)
    {
        if (FSigilVFXInstance* Instance = ActiveVFXInstances.Find(InstanceID))
        {
            // Desativar componente Niagara se ainda válido
            if (IsValid(Instance->NiagaraComponent.Get()))
            {
                UNiagaraComponent* Component = Instance->NiagaraComponent.Get();
                if (IsValid(Component))
                {
                    Component->Deactivate();

                    // Retornar ao pool se pooling estiver habilitado
                    if (bUsePooling)
                    {
                        ReturnComponentToPool(Component, Instance->VFXType);
                    }
                }
            }

            // Disparar evento de finalização
            // OnVFXFinished.Broadcast(Instance->VFXType, InstanceID); // Delegate precisa ser declarado

            // Remover da lista ativa
            ActiveVFXInstances.Remove(InstanceID);
            RemovedCount++;
        }
    }

    if (RemovedCount > 0)
    {
        UE_LOGFMT(LogTemp, Verbose, "Cleaned up {0} expired VFX instances", RemovedCount);
    }
}

void USigilVFXManager::ReturnComponentToPool(UNiagaraComponent* Component, ESigilVFXType VFXType)
{
    if (!Component || !bUsePooling)
    {
        return;
    }

    // Resetar componente para estado inicial
    Component->Deactivate();
    Component->SetVisibility(false);
    Component->SetComponentTickEnabled(false);
    Component->DetachFromComponent(FDetachmentTransformRules::KeepWorldTransform);

    // Adicionar de volta ao pool
    // VFXComponentPools precisa ser declarado como membro da classe
    // if (TArray<TObjectPtr<UNiagaraComponent>>* Pool = VFXComponentPools.Find(VFXType))
    {
        // Verificar se o pool não está cheio
        // int32 OptimalSize = GetOptimalPoolSize(VFXType); // Método precisa ser implementado
        if (Pool->Num() < OptimalSize * 1.2f) // Permitir 20% acima do ótimo
        {
            Pool->Add(Component);
            UE_LOGFMT(LogTemp, VeryVerbose, "Returned component to pool for VFX type {0}", static_cast<int32>(VFXType));
        }
        else
        {
            // Pool está cheio, destruir componente
            Component->DestroyComponent();
            UE_LOGFMT(LogTemp, VeryVerbose, "Pool full, destroyed component for VFX type {0}", static_cast<int32>(VFXType));
        }
    }
    else
    {
        // Pool não existe, destruir componente
        Component->DestroyComponent();
    }
}

void USigilVFXManager::ForceCleanupAllVFX()
{
    UE_LOGFMT(LogTemp, Log, "Force cleanup of all VFX instances ({0} active)", ActiveVFXInstances.Num());

    // Desativar e limpar todas as instâncias ativas
    for (auto& InstancePair : ActiveVFXInstances)
    {
        FSigilVFXInstance& Instance = InstancePair.Value;

        if (IsValid(Instance.NiagaraComponent.Get()))
        {
            UNiagaraComponent* Component = Instance.NiagaraComponent.Get();
            if (IsValid(Component))
            {
                Component->Deactivate();
                Component->DestroyComponent();
            }
        }

        // Disparar evento de finalização
        // OnVFXFinished.Broadcast(Instance.VFXType, Instance.InstanceID); // Delegate precisa ser declarado
    }

    // Limpar lista de instâncias ativas
    ActiveVFXInstances.Empty();

    UE_LOGFMT(LogTemp, Log, "Force cleanup completed - all VFX instances removed");
}